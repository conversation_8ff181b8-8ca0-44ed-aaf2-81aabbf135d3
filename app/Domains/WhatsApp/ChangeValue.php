<?php

namespace App\Domains\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;

class ChangeValue
{
    public array $metadata;
    public array $messages;
    public array $statuses;
    public array $contacts;
    public ?string $primaryWamId;

    public function __construct(array $changeValueData)
    {
        $this->metadata = $changeValueData['metadata'] ?? [];
        $this->messages = $changeValueData['messages'] ?? [];
        $this->statuses = $changeValueData['statuses'] ?? [];
        $this->contacts = $changeValueData['contacts'] ?? [];
        $this->primaryWamId = $this->extractPrimaryWamId();
    }

    /**
     * Get phone number ID from metadata
     */
    public function getPhoneNumberId(): ?string
    {
        return $this->metadata['phone_number_id'] ?? null;
    }

    /**
     * Check if has messages
     */
    public function hasMessages(): bool
    {
        return !empty($this->messages);
    }

    /**
     * Check if has statuses
     */
    public function hasStatuses(): bool
    {
        return !empty($this->statuses);
    }

    /**
     * Get incoming messages (filter out outgoing)
     */
    public function getIncomingMessages(PhoneNumber $phoneNumber): array
    {
        return array_filter($this->messages, function ($message) use ($phoneNumber) {
            return !$this->isOutgoingMessage($message, $phoneNumber);
        });
    }

    /**
     * Check if message is outgoing (from business)
     */
    public function isOutgoingMessage(array $message, PhoneNumber $phoneNumber): bool
    {
        $messageFrom = $message['from'] ?? '';
        $businessNumber = $phoneNumber->phone_number ?? '';

        $normalizedMessageFrom = preg_replace('/[^0-9]/', '', $messageFrom);
        $normalizedBusinessNumber = preg_replace('/[^0-9]/', '', $businessNumber);

        return $normalizedMessageFrom === $normalizedBusinessNumber;
    }

    /**
     * Extract status summary
     */
    public function extractStatusSummary(): array
    {
        $summary = [
            'total' => count($this->statuses),
            'by_status' => [],
            'message_ids' => []
        ];

        foreach ($this->statuses as $status) {
            $statusType = $status['status'] ?? 'unknown';
            $messageId = $status['id'] ?? null;

            if (!isset($summary['by_status'][$statusType])) {
                $summary['by_status'][$statusType] = 0;
            }
            $summary['by_status'][$statusType]++;

            if ($messageId) {
                $summary['message_ids'][] = $messageId;
            }
        }

        return $summary;
    }

    /**
     * Get event type based on content
     */
    public function getEventType(): string
    {
        if ($this->hasMessages()) {
            return 'message';
        } elseif ($this->hasStatuses()) {
            return 'status';
        }

        return 'other';
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'metadata' => $this->metadata,
            'messages' => $this->messages,
            'statuses' => $this->statuses,
            'contacts' => $this->contacts
        ];
    }

    /**
     * Validate structure for message changes
     */
    public function validateMessageChange(): bool
    {
        // Should have metadata with phone_number_id
        if (!isset($this->metadata['phone_number_id'])) {
            return false;
        }

        // Should have either messages or statuses
        if (!$this->hasMessages() && !$this->hasStatuses()) {
            return false;
        }

        // Validate message structure if present
        if ($this->hasMessages()) {
            foreach ($this->messages as $message) {
                if (!$this->validateMessageStructure($message)) {
                    return false;
                }
            }
        }

        // Validate status structure if present
        if ($this->hasStatuses()) {
            foreach ($this->statuses as $status) {
                if (!$this->validateStatusStructure($status)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate individual message structure
     */
    private function validateMessageStructure(array $message): bool
    {
        $requiredFields = ['id', 'from', 'timestamp', 'type'];

        foreach ($requiredFields as $field) {
            if (!isset($message[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validate individual status structure
     */
    private function validateStatusStructure(array $status): bool
    {
        $requiredFields = ['id', 'status', 'timestamp', 'recipient_id'];

        foreach ($requiredFields as $field) {
            if (!isset($status[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Extract primary WAM ID from statuses or messages
     */
    private function extractPrimaryWamId(): ?string
    {
        // First try to get from statuses (most common for status updates)
        if (!empty($this->statuses)) {
            return $this->statuses[0]['id'] ?? null;
        }

        // Fallback to messages if no statuses
        if (!empty($this->messages)) {
            return $this->messages[0]['id'] ?? null;
        }

        return null;
    }

    /**
     * Get primary WAM ID
     */
    public function getPrimaryWamId(): ?string
    {
        return $this->primaryWamId;
    }

    /**
     * Obtém o status mais recente dos status disponíveis
     */
    public function getLatestStatus(): ?array
    {
        if (empty($this->statuses)) {
            return null;
        }

        // Retorna o primeiro status (mais recente)
        return $this->statuses[0];
    }
}
