# WhatsApp Webhook Controller - Fluxo Completo Explicado

## 🎯 **Visão Geral**

O `WhatsAppWebhookController` é o ponto de entrada para todos os eventos do WhatsApp Business API. Ele processa dois tipos de requisições: **verificação de webhook** (GET) e **eventos de webhook** (POST).

---

## 🔄 **Fluxo Principal - Passo a Passo**

### **1. Entrada do Webhook**

#### **Verificação (GET Request)**
```
GET /api/whatsapp/webhook?hub_mode=subscribe&hub_verify_token=TOKEN&hub_challenge=123456
```

**Processo:**
1. **Extração de Parâmetros**: `hub_mode`, `hub_verify_token`, `hub_challenge`
2. **Validação via UseCase**: `VerifyWebhookToken::perform()`
3. **Log do Evento**: `LogWebhookEvent` registra tentativa
4. **Resposta**: 
   - ✅ **Sucesso**: Retorna o `challenge` number
   - ❌ **Falha**: Retorna 403 Forbidden

#### **Eventos (POST Request)**
```
POST /api/whatsapp/webhook
Headers: X-Hub-Signature-256: sha256=...
Body: { "object": "whatsapp_business_account", "entry": [...] }
```

---

### **2. Validação de Segurança**

#### **2.1 Validação de Assinatura**
```php
// Extrai header de assinatura
$signature = $request->header('X-Hub-Signature-256');
$payload = $request->getContent();

// Valida via UseCase
$validateWebhookSignature = app()->make(ValidateWebhookSignature::class);
if (!$validateWebhookSignature->perform($payload, $signature)) {
    // Log security failure e retorna 403
}
```

**Como funciona:**
- Usa `WHATSAPP_WEBHOOK_SECRET` do `.env`
- Gera HMAC SHA-256 do payload
- Compara com assinatura recebida usando `hash_equals()`

#### **2.2 Validação de Estrutura**
```php
$validateWebhookPayload = app()->make(ValidateWebhookPayload::class);
if (!$validateWebhookPayload->perform($webhookData)) {
    // Log invalid data e retorna 400
}
```

**Validações:**
- ✅ `object` deve ser `"whatsapp_business_account"`
- ✅ `entry` deve existir e ser array
- ✅ Cada `entry` deve ter `changes`
- ✅ Cada `change` deve ter `field` e `value`

---

### **3. Processamento de Entries**

#### **3.1 Loop Principal**
```php
foreach ($webhookData['entry'] ?? [] as $entry) {
    foreach ($entry['changes'] ?? [] as $change) {
        if ($change['field'] === 'messages') {
            $result = $this->processChange($change['value']);
        }
    }
}
```

**Tipos de Change Field:**
- 📱 **`messages`**: Mensagens recebidas/enviadas
- 📊 **`message_status`**: Status de entrega
- ⚙️ **Outros**: Configurações, etc.

#### **3.2 Método processChange()**

**Passo 1: Criar ChangeValue Domain**
```php
$changeValue = new ChangeValue($changeValueData);
$phoneNumberId = $changeValue->getPhoneNumberId();
```

**Passo 2: Identificar Organização**
```php
$fetchOrganizationFromPhoneNumber = app()->make(FetchOrganizationFromPhoneNumber::class);
$identificationResult = $fetchOrganizationFromPhoneNumber->perform($phoneNumberId);
```

**Passo 3: Log do Evento**
```php
$logWebhookEvent = app()->make(LogWebhookEvent::class);
$logWebhookEvent->perform(
    $organization->id,
    $phoneNumberId,
    $changeValue->getEventType(),
    $changeValue->toArray(),
    'pending'
);
```

---

### **4. Roteamento por Tipo de Evento**

#### **4.1 Mensagens (hasMessages)**
```php
if ($changeValue->hasMessages()) {
    $processWebhookMessage = app()->make(ProcessWebhookMessage::class);
    return $processWebhookMessage->perform($changeValueData, $organization, $phoneNumber);
}
```

**Fluxo de Mensagens:**
1. **Filtrar Mensagens**: Apenas incoming (não outgoing)
2. **Loop por Mensagem**: Processa cada mensagem individualmente
3. **Criar ChangeValueMessage**: Domain object para cada mensagem
4. **Preparar Dados**: Monta array para ChatBotService
5. **Processar via ChatBot**: `ChatBotService::processWebhook()`

#### **4.2 Status Updates (hasStatuses)**
```php
elseif ($changeValue->hasStatuses()) {
    $processWebhookStatus = app()->make(ProcessWebhookStatus::class);
    return $processWebhookStatus->perform($changeValueData, $organization, $phoneNumber);
}
```

**Fluxo de Status:**
- Atualiza status de entrega de mensagens
- Processa confirmações de leitura
- Registra falhas de entrega

---

### **5. Processamento via ChatBot**

#### **5.1 ChatBotService Pipeline**
```php
$result = $this->chatBotService->processWebhook([
    'message' => $message->toArray(),
    'metadata' => $changeValue->metadata,
    'contacts' => $changeValue->contacts,
    'organization' => $organization,
    'phone_number' => $phoneNumber
]);
```

#### **5.2 Pipeline Interno do ChatBot**
1. **ProcessWebhookMessage**: Valida e processa mensagem
2. **FindOrCreateClient**: Busca/cria cliente baseado no telefone
3. **FindOrCreateConversation**: Busca/cria conversa ativa
4. **ProcessFlowStep**: Processa step atual do fluxo
5. **SendWhatsAppResponse**: Envia resposta ao cliente

---

### **6. Tipos de Step Processing**

#### **6.1 Message Steps**
- Envia mensagem simples
- Move para próximo step automaticamente

#### **6.2 Interactive Steps**
- Envia botões/listas
- Aguarda interação do usuário
- Navega baseado na escolha

#### **6.3 Input Steps**
- Coleta dados do usuário
- Atualiza campos do cliente
- Valida input recebido

#### **6.4 Command Steps**
- Executa lógica de negócio
- Pode integrar com sistemas externos
- Retorna resultado da execução

---

### **7. Resposta Final**

#### **7.1 Sucesso**
```json
{
    "status": "success",
    "processed": 2,
    "results": [
        {
            "success": true,
            "conversation_id": 123,
            "client_id": 456,
            "step_result": {...},
            "response": {...}
        }
    ]
}
```

#### **7.2 Erro**
```json
{
    "status": "error",
    "message": "Internal server error"
}
```

---

## 🔍 **Componentes Principais**

### **Use Cases Utilizados**
- ✅ **VerifyWebhookToken**: Validação de token de verificação
- ✅ **ValidateWebhookSignature**: Validação HMAC da assinatura
- ✅ **ValidateWebhookPayload**: Validação da estrutura do payload
- ✅ **FetchOrganizationFromPhoneNumber**: Identificação da organização
- ✅ **LogWebhookEvent**: Logging de todos os eventos
- ✅ **ProcessWebhookMessage**: Processamento de mensagens
- ✅ **ProcessWebhookStatus**: Processamento de status updates

### **Domains Utilizados**
- ✅ **ChangeValue**: Representa dados do webhook
- ✅ **ChangeValueMessage**: Representa mensagem individual
- ✅ **Organization**: Organização proprietária do número
- ✅ **PhoneNumber**: Número WhatsApp Business

### **Services Integrados**
- ✅ **ChatBotService**: Orquestrador principal do ChatBot
- ✅ **WhatsAppWebhookLog**: Sistema de logging

---

## 🚨 **Pontos de Falha e Tratamento**

### **Falhas de Segurança**
- **Assinatura Inválida**: 403 + Log security
- **Token Inválido**: 403 + Log verification failed

### **Falhas de Dados**
- **Payload Inválido**: 400 + Log invalid data
- **Organização Não Encontrada**: Skip processing
- **Phone Number Não Encontrado**: Skip processing

### **Falhas de Processamento**
- **Erro no ChatBot**: 500 + Log error + Continue
- **Erro no UseCase**: 500 + Log error + Continue

### **Logging Completo**
Todos os eventos são logados via `LogWebhookEvent`:
- ✅ **Verificações** (success/failed)
- ✅ **Falhas de Segurança** (invalid signature)
- ✅ **Dados Inválidos** (malformed payload)
- ✅ **Processamento** (pending/success/failed)

---

## 📊 **Métricas e Monitoramento**

### **Logs Gerados**
- **WhatsAppWebhookLog**: Todos os eventos com payload completo
- **Laravel Log**: Erros e exceções
- **DBLog**: Operações importantes

### **Status Tracking**
- **pending**: Evento recebido, processamento iniciado
- **success**: Processamento concluído com sucesso
- **failed**: Processamento falhou com erro

### **Dados Coletados**
- **organization_id**: Organização responsável
- **phone_number_id**: Número WhatsApp
- **event_type**: Tipo do evento (message/status/other)
- **webhook_payload**: Payload completo do webhook
- **processing_status**: Status do processamento
- **error_message**: Mensagem de erro (se houver)

---

## 🎯 **Resumo do Fluxo**

1. **Webhook chega** → Validação de segurança
2. **Payload validado** → Extração de entries
3. **Loop por changes** → Identificação de mensagens
4. **Organização identificada** → Log do evento
5. **Tipo determinado** → Roteamento (message/status)
6. **ChatBot processa** → Pipeline completo
7. **Resposta enviada** → Cliente recebe resposta
8. **Log atualizado** → Status final registrado

O sistema é **robusto**, **bem logado** e **tolerante a falhas**, garantindo que webhooks sejam processados de forma confiável mesmo em cenários de erro! 🚀
