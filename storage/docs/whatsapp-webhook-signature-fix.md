# 🔐 WhatsApp Webhook Signature - Problema e Solução

## 🚨 **Problema Identificado**

O webhook está falhando na validação de assinatura HMAC SHA-256. O erro mostra:

```
WhatsApp webhook failed signature validation
```

**Assinatura recebida**: `sha256=642192cb0e137078766784eddd25800fc50f5296522def7f4c0fe93c1713598e`

## 🔍 **Como Funciona a Validação**

### **1. Processo de Validação**
```php
// No ValidateWebhookSignature UseCase:
$secret = config('whatsapp.webhook_secret');  // Pega do .env
$expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
return hash_equals($expectedSignature, $signature);  // Compara
```

### **2. O que o WhatsApp Faz**
1. WhatsApp pega o **webhook secret** que você configurou no Meta Business
2. Gera HMAC SHA-256 do payload usando esse secret
3. Envia no header `X-Hub-Signature-256: sha256=...`

### **3. O que Nosso Sistema Faz**
1. Pega o secret do `.env` (`WHATSAPP_WEBHOOK_SECRET`)
2. Gera HMAC SHA-256 do mesmo payload
3. Compara com a assinatura recebida

## 🎯 **Onde Encontrar o Webhook Secret Correto**

### **1. No Meta Business Manager**
1. Acesse [Meta Business Manager](https://business.facebook.com/)
2. Vá em **WhatsApp Business Platform**
3. Selecione seu app
4. Vá em **Configuração > Webhooks**
5. O **Webhook Secret** está lá

### **2. Estrutura Típica do Secret**
- Geralmente é uma string aleatória de 32-64 caracteres
- Exemplo: `abc123def456ghi789jkl012mno345pqr678stu901vwx234yz`

## 🔧 **Como Corrigir**

### **1. Atualizar o .env**
```env
# Substitua "your_webhook_secret" pelo secret real do Meta Business
WHATSAPP_WEBHOOK_SECRET="SEU_SECRET_REAL_AQUI"
```

### **2. Verificar Configuração Atual**
```bash
# Verificar se a variável está definida
php artisan tinker
>>> config('whatsapp.webhook_secret')
```

### **3. Limpar Cache de Configuração**
```bash
php artisan config:clear
php artisan config:cache
```

## 🧪 **Como Testar se Está Correto**

### **1. Criar Script de Teste**
Crie um arquivo temporário para testar:

```php
<?php
// test_signature.php

$payload = '{"object":"whatsapp_business_account","entry":[{"id":"****************","changes":[{"value":{"messaging_product":"whatsapp","metadata":{"display_phone_number":"***********","phone_number_id":"***************"},"statuses":[{"id":"wamid.********************************************************","status":"delivered","timestamp":"**********","recipient_id":"************","conversation":{"id":"62b150cad622664fc6b49210b55fd3c1","origin":{"type":"marketing"}},"pricing":{"billable":true,"pricing_model":"PMP","category":"marketing","type":"regular"}}]},"field":"messages"}]}]}';

$receivedSignature = 'sha256=642192cb0e137078766784eddd25800fc50f5296522def7f4c0fe93c1713598e';

$secret = 'SEU_SECRET_AQUI'; // Substitua pelo secret real

$expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

echo "Received:  " . $receivedSignature . "\n";
echo "Expected:  " . $expectedSignature . "\n";
echo "Match:     " . ($receivedSignature === $expectedSignature ? 'YES' : 'NO') . "\n";
```

### **2. Executar Teste**
```bash
php test_signature.php
```

## 🔍 **Debugging Avançado**

### **1. Adicionar Logs Temporários**
No `ValidateWebhookSignature.php`, adicione logs:

```php
public function perform(string $payload, ?string $signature): bool
{
    try {
        $secret = config('whatsapp.webhook_secret');
        
        // DEBUG: Log valores
        \Log::info('DEBUG Signature Validation', [
            'secret_length' => strlen($secret ?? ''),
            'secret_first_10' => substr($secret ?? '', 0, 10),
            'received_signature' => $signature,
            'payload_length' => strlen($payload)
        ]);

        if (!$signature || !$secret) {
            \Log::warning('DEBUG: Missing signature or secret');
            return false;
        }

        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
        
        \Log::info('DEBUG: Signature comparison', [
            'expected' => $expectedSignature,
            'received' => $signature,
            'match' => hash_equals($expectedSignature, $signature)
        ]);

        return hash_equals($expectedSignature, $signature);
    } catch (Throwable $e) {
        // ... resto do código
    }
}
```

## 🚨 **Problemas Comuns**

### **1. Secret Não Definido**
```
WHATSAPP_WEBHOOK_SECRET="your_webhook_secret"  // ❌ Placeholder
```
**Solução**: Substituir pelo secret real do Meta Business

### **2. Secret com Espaços/Quebras**
```
WHATSAPP_WEBHOOK_SECRET="abc123 def456"  // ❌ Com espaço
```
**Solução**: Remover espaços e quebras de linha

### **3. Secret Incorreto**
```
WHATSAPP_WEBHOOK_SECRET="secret_errado"  // ❌ Não é o mesmo do Meta
```
**Solução**: Copiar exatamente do Meta Business Manager

### **4. Cache de Configuração**
O Laravel pode estar usando cache antigo.
**Solução**: `php artisan config:clear`

## 📋 **Checklist de Verificação**

- [ ] Secret copiado corretamente do Meta Business Manager
- [ ] Sem espaços ou quebras de linha no secret
- [ ] Variável `WHATSAPP_WEBHOOK_SECRET` definida no .env
- [ ] Cache de configuração limpo
- [ ] Teste de assinatura executado com sucesso

## 🎯 **Próximos Passos**

### **1. Imediato**
1. Encontrar o webhook secret correto no Meta Business
2. Atualizar o `.env`
3. Limpar cache: `php artisan config:clear`
4. Testar novamente

### **2. Verificação**
1. Executar script de teste de assinatura
2. Verificar logs de debug
3. Confirmar que webhooks passam na validação

### **3. Limpeza**
1. Remover logs de debug temporários
2. Remover script de teste
3. Documentar o secret correto (sem expor)

## 💡 **Dica Importante**

O **webhook secret** deve ser **exatamente o mesmo** que você configurou no Meta Business Manager. Qualquer diferença (espaço, caractere, etc.) fará a validação falhar.

Se você não lembra qual secret foi configurado, pode:
1. Gerar um novo secret no Meta Business
2. Atualizar tanto no Meta quanto no `.env`
3. Testar novamente

## 🔐 **Segurança**

- ✅ **NUNCA** commite o secret real no Git
- ✅ Use `.env` para valores sensíveis
- ✅ Mantenha o secret seguro e privado
- ✅ Rotacione o secret periodicamente
