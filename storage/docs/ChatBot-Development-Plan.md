# 🤖 ChatBot Module Development Plan

## Overview
This document outlines a comprehensive development plan to improve our ChatBot module by building complete test coverage and enhancing Postman documentation. The plan covers 21 ChatBot entities with a focus on making our WhatsApp automation platform more developer-friendly.

## 🎯 Goals
- Build unit and integration/feature tests for all ChatBot entities
- Complete test coverage for UseCases, Domains, Factories, and Repositories
- Improve Postman documentation with complete API field coverage
- Ensure all WhatsApp automation endpoints have proper request/response examples

## 📋 ChatBot Entities (21 Total)

### Core Entities
1. **Flow** - Conversation flow management with step sequences
2. **Step** - Individual steps within flows with conditional logic
3. **Component** - Message components (header, body, footer, buttons)
4. **Button** - Interactive buttons with actions and conditions
5. **Parameter** - Dynamic parameters for message personalization
6. **Template** - WhatsApp message templates with components
7. **Campaign** - Marketing campaigns with client targeting
8. **Message** - Individual messages with delivery tracking
9. **Conversation** - Active chat sessions with clients
10. **Interaction** - User interaction tracking and analytics
11. **PhoneNumber** - WhatsApp Business phone number management

### Advanced Entities
12. **Category** - Campaign categorization system
13. **Tag** - Flexible tagging system for campaigns
14. **CampaignCategoryAssignment** - Campaign-category relationships
15. **CampaignTagAssignment** - Campaign-tag relationships
16. **CampaignAnalytics** - Campaign performance metrics
17. **CampaignStatusHistory** - Campaign status change tracking
18. **MessageDeliveryAttempt** - Message delivery retry tracking
19. **WhatsAppSyncLog** - WhatsApp synchronization logging
20. **TemplatePublishing** - Template publishing workflow
21. **Lead** - Lead generation and management

---

## 🚀 Development Tasks

### Task 1: Flow Entity
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Complete with complex step relationships
- ✅ UseCases: Complete CRUD + SaveFullFlow + GetDefault
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/FlowTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/FlowFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/FlowRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Flow/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Flow/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Flow/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Flow/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Flow/SaveFullFlowTest.php`
- [ ] Create `tests/Feature/ChatBot/Flow/GetDefaultTest.php`

**Postman Documentation:**
- [ ] Audit existing flow endpoints against Flow domain fields
- [ ] Add all Flow fields: name, description, is_active, organization_id, phone_number_id
- [ ] Include step relationships and SaveFullFlow examples
- [ ] Add GetDefault endpoint documentation

---

### Task 2: Step Entity
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Complex entity with flow relationships and conditional logic
- ✅ UseCases: Complete CRUD + SaveFullStep
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/StepTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/StepFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/StepRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Step/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Step/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Step/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Step/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Step/SaveFullStepTest.php`

**Postman Documentation:**
- [ ] Add all Step fields: step, name, description, is_input, is_condition, flow_id
- [ ] Include component and button relationships
- [ ] Add conditional logic examples
- [ ] Document SaveFullStep with nested components

---

### Task 3: Component Entity
**Priority: High** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Complex with type constants and parameter handling
- ✅ UseCases: Complete CRUD + SaveFullComponent + SaveFullTemplateComponent
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/ComponentTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/ComponentFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/ComponentRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Component/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Component/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Component/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Component/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Component/SaveFullComponentTest.php`
- [ ] Create `tests/Feature/ChatBot/Component/SaveFullTemplateComponentTest.php`

**Postman Documentation:**
- [ ] Add all Component fields: type, sub_type, index, text, format, json
- [ ] Include HEADER, BODY, FOOTER, BUTTONS type examples
- [ ] Document parameter and button relationships
- [ ] Add SaveFullComponent and SaveFullTemplateComponent endpoints

---

### Task 4: Button Entity
**Priority: High** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Complex with action types and conditional logic
- ✅ UseCases: Complete CRUD + SaveFullButton
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/ButtonTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/ButtonFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/ButtonRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Button/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Button/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Button/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Button/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Button/SaveFullButtonTest.php`

**Postman Documentation:**
- [ ] Add all Button fields: text, type, internal_type, internal_data, step_id
- [ ] Include action type examples (URL, PHONE_NUMBER, etc.)
- [ ] Document conditional logic with internal_type
- [ ] Add SaveFullButton endpoint with examples

---

### Task 5: Parameter Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Simple entity for dynamic content
- ✅ UseCases: Complete CRUD + SaveFullParameter
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/ParameterTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/ParameterFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/ParameterRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Parameter/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Parameter/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Parameter/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Parameter/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Parameter/SaveFullParameterTest.php`

**Postman Documentation:**
- [ ] Add all Parameter fields: name, value, type, component_id
- [ ] Include dynamic content examples
- [ ] Document variable substitution patterns
- [ ] Add SaveFullParameter endpoint

---

### Task 6: Template Entity
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Complex with WhatsApp integration and component relationships
- ✅ UseCases: Complete CRUD + SaveFullTemplate + PublishTemplate + RePublishTemplate
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/TemplateTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/TemplateFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/TemplateRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Template/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Template/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Template/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Template/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Template/SaveFullTemplateTest.php`
- [ ] Create `tests/Feature/ChatBot/Template/PublishTemplateTest.php`
- [ ] Create `tests/Feature/ChatBot/Template/RePublishTemplateTest.php`

**Postman Documentation:**
- [ ] Add all Template fields: name, category, parameter_format, language, library_template_name, id_external, status
- [ ] Include WhatsApp integration fields
- [ ] Document component relationships
- [ ] Add publishing workflow endpoints

---

### Task 7: Campaign Entity
**Priority: High** | **Estimated Time: 6-7 hours**

#### Current Status
- ✅ Domain: Complex with client relationships and status tracking
- ✅ UseCases: Complete CRUD + GenerateMessages + LaunchCampaign + Cancel + AssignCategories + AssignTags + AddClientsToCampaign + RemoveClientFromCampaign + GetStatusHistory + UpdateStatusFromMessages
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Enhanced endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/CampaignTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/CampaignFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/CampaignRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Campaign/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/GenerateMessagesTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/LaunchCampaignTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/CancelTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/AssignCategoriesTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/AssignTagsTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/AddClientsToCampaignTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/RemoveClientFromCampaignTest.php`
- [ ] Create `tests/Feature/ChatBot/Campaign/GetStatusHistoryTest.php`

**Postman Documentation:**
- [ ] Add all Campaign fields: name, description, status, is_direct_message, template_id, phone_number_id
- [ ] Include client management endpoints
- [ ] Document category and tag assignment
- [ ] Add campaign lifecycle endpoints (launch, cancel, status tracking)

---

### Task 8: Message Entity
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Complex with delivery tracking and retry logic
- ✅ UseCases: Complete CRUD + Send + Resend + ResendFailed + GetByCampaign + GetMessagesAvailableToSent
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Enhanced endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/MessageTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/MessageFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/MessageRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Message/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/GetAllTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/SendTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/ResendTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/ResendFailedTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/GetByCampaignTest.php`
- [ ] Create `tests/Feature/ChatBot/Message/GetMessagesAvailableToSentTest.php`

**Postman Documentation:**
- [ ] Add all Message fields: message, status, is_sent, is_fail, is_read, is_direct_message, delivery_attempts, max_retries
- [ ] Include delivery tracking fields
- [ ] Document retry logic and failure handling
- [ ] Add specialized message operation endpoints

---

### Task 9: Conversation Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Session management entity
- ✅ UseCases: Complete CRUD operations
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/ConversationTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/ConversationFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/ConversationRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Conversation/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Conversation/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Conversation/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Conversation/GetAllTest.php`

**Postman Documentation:**
- [ ] Add all Conversation fields: client_id, phone_number_id, flow_id, current_step_id, status
- [ ] Include session management examples
- [ ] Document conversation state tracking

---

### Task 10: Interaction Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: User interaction tracking
- ✅ UseCases: Complete CRUD operations
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/ChatBot/InteractionTest.php`
- [ ] Create `tests/Unit/Factories/ChatBot/InteractionFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/InteractionRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/ChatBot/Interaction/StoreTest.php`
- [ ] Create `tests/Feature/ChatBot/Interaction/UpdateTest.php`
- [ ] Create `tests/Feature/ChatBot/Interaction/DeleteTest.php`
- [ ] Create `tests/Feature/ChatBot/Interaction/GetAllTest.php`

**Postman Documentation:**
- [ ] Add all Interaction fields: conversation_id, step_id, button_id, user_input, interaction_type
- [ ] Include analytics and tracking examples
- [ ] Document interaction event types

---

## 🛠️ Implementation Guidelines

### Critical Testing Requirements
1. **Circular Dependency Prevention**:
   - Always use `app()->make()` when encountering circular import issues
   - Test factory resolution through service container in every factory test
   - Avoid direct instantiation of factories with complex dependencies

2. **Factory Service Container Testing**:
   - Every factory MUST have a test verifying `app()->make(FactoryClass::class)` works
   - Example test pattern:
   ```php
   public function test_factory_can_be_resolved_via_service_container()
   {
       $factory = app()->make(ChatBotEntityFactory::class);
       $this->assertInstanceOf(ChatBotEntityFactory::class, $factory);
   }
   ```

3. **Postman Field Coverage Validation**:
   - **Domain Cross-Check**: For every endpoint, verify against the domain class that ALL properties are represented
   - **Existing Endpoint Audit**: Review existing Postman endpoints for missing fields
   - **WhatsApp Integration**: Include all WhatsApp-specific fields and payload examples

### ChatBot-Specific Guidelines

#### WhatsApp Integration Testing
- Mock WhatsApp API calls in tests
- Test payload generation methods (toWhatsAppTemplatePayload, toWhatsAppDirectPayload)
- Validate webhook processing logic
- Test message delivery and retry mechanisms

#### Complex Relationship Testing
- Test SaveFull* use cases with nested entities
- Validate flow-step-component-button relationships
- Test campaign-message generation and delivery
- Verify template publishing workflows

#### Business Logic Testing
- Test conditional navigation in flows
- Validate input collection and client field updates
- Test campaign lifecycle (draft → active → completed → cancelled)
- Verify message retry logic and failure handling

### Test Organization
```
tests/
├── Unit/
│   ├── Domains/ChatBot/
│   ├── Factories/ChatBot/
│   └── Repositories/ChatBot/
└── Feature/
    └── ChatBot/
        ├── Flow/
        ├── Step/
        ├── Component/
        ├── Button/
        ├── Campaign/
        ├── Message/
        └── ... (one folder per entity)
```

### Priority Implementation Order

#### Phase 1: Core Flow Management (Weeks 1-2)
1. **Flow** - Foundation of conversation logic
2. **Step** - Individual conversation steps
3. **Component** - Message building blocks
4. **Button** - Interactive elements
5. **Parameter** - Dynamic content

#### Phase 2: Template & Campaign System (Weeks 3-4)
1. **Template** - WhatsApp message templates
2. **Campaign** - Marketing campaign management
3. **Message** - Individual message handling
4. **PhoneNumber** - WhatsApp Business integration

#### Phase 3: Analytics & Advanced Features (Week 5)
1. **Conversation** - Session management
2. **Interaction** - User tracking
3. **Category/Tag** - Organization system
4. **Analytics** - Performance tracking
5. **Advanced entities** - Specialized features

### Success Metrics
- [ ] 100% test coverage for all ChatBot entities
- [ ] All SaveFull* use cases properly tested
- [ ] Complete WhatsApp integration testing
- [ ] All Postman endpoints include complete field coverage
- [ ] Complex business logic thoroughly validated
- [ ] Factory service container resolution verified

### Next Steps
1. **FIRST**: Audit existing Postman endpoints against domain fields
2. Start with **Flow** entity as it's the foundation
3. **MANDATORY**: Implement factory service container test for every factory
4. Focus on SaveFull* use cases for comprehensive integration testing
5. Test WhatsApp payload generation methods
6. Validate complex business logic and relationships

### Critical Implementation Notes
- **WhatsApp Integration**: Mock external API calls, test payload generation
- **Complex Dependencies**: Use app()->make() for factories with multiple dependencies
- **Business Logic**: Test conditional flows, input validation, and state management
- **Field Completeness**: Every endpoint must include ALL domain fields
- **Relationship Testing**: Validate nested entity creation and updates
