# Documentação Completa - Campanhas WhatsApp

## Visão Geral

O sistema de campanhas WhatsApp é uma solução robusta e completa para criação, gestão e envio automatizado de mensagens em massa através da WhatsApp Business API. O sistema oferece funcionalidades avançadas de categorização, sincronização proativa, analytics detalhados e controle granular de status.

## Arquitetura do Sistema

### Estrutura de Domínios

#### 1. **Campaign Domain** (`app/Domains/ChatBot/Campaign.php`)
Representa uma campanha de mensagens WhatsApp com todas suas propriedades e comportamentos.

**Propriedades principais:**
- `id`, `organization_id`, `user_id`, `template_id`, `phone_number_id`
- `name`, `description` - Identificação da campanha
- `is_scheduled`, `is_sent`, `is_sending`, `is_direct_message` - Flags de controle
- `status` - Enum CampaignStatus (DRAFT, SCHEDULED, SENDING, COMPLETED, FAILED, CANCELLED)
- `message_count` - <PERSON>tador de mensagens
- `sent_at`, `scheduled_at`, `cancelled_at`, `failed_at` - Timestamps de controle

**Relacionamentos:**
- `template` - Template WhatsApp utilizado
- `phone_number` - Número de telefone remetente
- `messages` - Mensagens individuais da campanha
- `clients` - Clientes destinatários
- `categories` - Categorias de organização
- `tags` - Tags de marcação
- `status_history` - Histórico de mudanças de status

**Métodos principais:**
- `getCurrentStatus()` - Obtém status atual (enum ou derivado de booleans)
- `canCancel()`, `canEdit()`, `canLaunch()` - Verificações de permissão
- `updateStatus()` - Atualiza status com histórico
- `hasFailedMessages()` - Verifica se há mensagens falhadas

#### 2. **Message Domain** (`app/Domains/ChatBot/Message.php`)
Representa uma mensagem individual dentro de uma campanha.

**Propriedades principais:**
- `id`, `organization_id`, `campaign_id`, `template_id`, `client_id`
- `message` - Conteúdo da mensagem processada
- `status` - Enum MessageStatus (is_draft, is_sent, is_failed)
- `is_sent`, `is_fail`, `is_read`, `is_direct_message` - Flags de status
- `delivery_attempts`, `max_retries` - Controle de retry
- `last_attempt_at`, `next_retry_at` - Timestamps de retry
- `last_error_message` - Última mensagem de erro

**Métodos principais:**
- `send()` - Marca mensagem como enviada
- `fail()` - Marca mensagem como falhada
- `canRetry()` - Verifica se pode tentar reenvio
- `scheduleRetry()` - Agenda próxima tentativa
- `toWhatsAppPayload()` - Gera payload para API WhatsApp

#### 3. **Template Domain** (`app/Domains/ChatBot/Template.php`)
Representa um template WhatsApp com componentes e parâmetros.

**Propriedades principais:**
- `id`, `organization_id`, `phone_number_id`, `user_id`
- `name`, `category`, `language` - Identificação do template
- `library_template_name`, `id_external` - Identificadores WhatsApp
- `status` - Status no WhatsApp (PENDING, APPROVED, REJECTED)
- `components` - Array de componentes (header, body, footer, buttons)

### Estrutura de Use Cases

#### Campanhas

1. **Store** (`app/UseCases/ChatBot/Campaign/Store.php`)
   - Cria nova campanha
   - Valida dados de entrada
   - Aplica regras de negócio

2. **Get** (`app/UseCases/ChatBot/Campaign/Get.php`)
   - Busca campanha por ID
   - Verifica permissões de organização

3. **Update** (`app/UseCases/ChatBot/Campaign/Update.php`)
   - Atualiza dados da campanha
   - Valida se campanha pode ser editada

4. **Delete** (`app/UseCases/ChatBot/Campaign/Delete.php`)
   - Remove campanha
   - Verifica dependências

5. **Cancel** (`app/UseCases/ChatBot/Campaign/Cancel.php`)
   - Cancela campanha antes/durante envio
   - Atualiza status e cria histórico

6. **LaunchCampaign** (`app/UseCases/ChatBot/Campaign/LaunchCampaign.php`)
   - Inicia envio da campanha
   - Gera mensagens individuais
   - Atualiza status para SENDING

7. **AddClientsToCampaign** (`app/UseCases/ChatBot/Campaign/AddClientsToCampaign.php`)
   - Adiciona clientes à campanha
   - Valida duplicatas

8. **GenerateMessages** (`app/UseCases/ChatBot/Campaign/GenerateMessages.php`)
   - Gera mensagens individuais para cada cliente
   - Processa parâmetros dinâmicos
   - Substitui variáveis do template

#### Mensagens

1. **Send** (`app/UseCases/ChatBot/Message/Send.php`)
   - Envia mensagem individual via WhatsApp API
   - Atualiza status da mensagem
   - Registra tentativas de envio

2. **Resend** (`app/UseCases/ChatBot/Message/Resend.php`)
   - Reenvia mensagem falhada
   - Controla tentativas de retry
   - Aplica backoff exponencial

3. **GetMessagesAvailableToSent** (`app/UseCases/ChatBot/Message/GetMessagesAvailableToSent.php`)
   - Busca mensagens prontas para envio
   - Aplica filtros de retry e agendamento

#### WhatsApp Sync

1. **SyncMessageStatus** (`app/UseCases/ChatBot/WhatsApp/SyncMessageStatus.php`)
   - Sincroniza status de mensagem específica
   - Consulta WhatsApp API
   - Atualiza status local

2. **SyncCampaignMessages** (`app/UseCases/ChatBot/WhatsApp/SyncCampaignMessages.php`)
   - Sincroniza todas as mensagens de uma campanha
   - Processa em lote
   - Gera relatório de sincronização

#### Analytics

1. **GetCampaignAnalytics** (`app/UseCases/ChatBot/Analytics/GetCampaignAnalytics.php`)
   - Calcula métricas de performance
   - Gera insights automáticos
   - Compara com médias organizacionais

2. **RecordEngagementEvent** (`app/UseCases/ChatBot/Analytics/RecordEngagementEvent.php`)
   - Registra eventos de engajamento
   - Processa webhooks de interação
   - Atualiza métricas em tempo real

### Repositories e Factories

#### CampaignRepository (`app/Repositories/CampaignRepository.php`)
**Métodos principais:**
- `store(Campaign $campaign)` - Persiste nova campanha
- `update(Campaign $campaign, int $organization_id)` - Atualiza campanha
- `fetchById(int $id)` - Busca por ID
- `fetchFullById(int $id)` - Busca com relacionamentos
- `delete(Campaign $campaign)` - Remove campanha
- `getAll(CampaignFilters $filters, OrderBy $orderBy)` - Listagem com filtros

#### CampaignFactory (`app/Factories/ChatBot/CampaignFactory.php`)
**Métodos principais:**
- `buildFromModel(CampaignModel $model, ...)` - Converte model para domain
- `buildFromStoreRequest(StoreRequest $request)` - Cria domain a partir de request
- `buildFromUpdateRequest(UpdateRequest $request)` - Atualiza domain a partir de request

### API Endpoints

#### Campanhas Base
```
GET    /api/campaigns                    - Lista campanhas com filtros
POST   /api/campaigns                    - Cria nova campanha
GET    /api/campaigns/{id}               - Busca campanha específica
PUT    /api/campaigns/{id}               - Atualiza campanha
DELETE /api/campaigns/{id}               - Remove campanha
```

#### Gestão de Campanhas
```
POST   /api/campaign/add-clients/{id}    - Adiciona clientes à campanha
POST   /api/campaign/remove-client/{id}  - Remove cliente da campanha
POST   /api/campaign/launch/{id}         - Lança campanha
GET    /api/campaign/{id}/clients        - Lista clientes da campanha
POST   /api/campaign/{id}/cancel         - Cancela campanha
GET    /api/campaign/{id}/status-history - Histórico de status
```

#### Mensagens
```
POST   /api/message/generate-messages/{campaign_id} - Gera mensagens da campanha
GET    /api/campaign/{id}/messages                  - Lista mensagens da campanha
GET    /api/campaign/{id}/messages/failed           - Lista mensagens falhadas
POST   /api/campaign/{id}/messages/resend-failed    - Reenvia mensagens falhadas
POST   /api/message/{id}/resend                     - Reenvia mensagem específica
GET    /api/message/{id}/delivery-status            - Status de entrega
```

#### Sincronização WhatsApp
```
POST   /api/whatsapp/sync/message/{id}      - Sincroniza mensagem específica
POST   /api/whatsapp/sync/campaign/{id}     - Sincroniza campanha completa
GET    /api/whatsapp/sync/logs              - Logs de sincronização
GET    /api/whatsapp/sync/entity-logs       - Logs por entidade
GET    /api/whatsapp/sync/trends            - Tendências de sincronização
GET    /api/whatsapp/sync/status-overview   - Visão geral do status
POST   /api/whatsapp/sync/trigger-proactive - Dispara sync proativo
```

#### Analytics
```
GET    /api/analytics/dashboard                     - Dashboard geral
GET    /api/analytics/campaign/{id}                 - Analytics de campanha
POST   /api/analytics/campaigns/multiple            - Analytics múltiplas campanhas
POST   /api/analytics/campaigns/compare             - Comparação de campanhas
POST   /api/analytics/engagement/record             - Registra evento de engajamento
POST   /api/analytics/engagement/bulk               - Registra eventos em lote
GET    /api/analytics/message/{id}/engagement       - Engajamento de mensagem
POST   /api/analytics/trigger-calculation           - Dispara cálculo de analytics
```

### Jobs e Cron Jobs

#### 1. **SendWhatsAppMessages** (Comando: `whatsapp:send-messages`)
- **Frequência:** A cada minuto
- **Função:** Envia mensagens pendentes
- **Processo:**
  1. Busca mensagens prontas para envio (limite configurável)
  2. Envia via WhatsApp API
  3. Atualiza status das mensagens
  4. Registra logs de sucesso/erro

#### 2. **ProactiveWhatsAppSync** (Job)
- **Frequência:** A cada 5 minutos
- **Função:** Sincronização proativa de status
- **Processo:**
  1. Identifica mensagens que precisam verificação
  2. Consulta WhatsApp API para status real
  3. Atualiza status local se necessário
  4. Registra logs de sincronização

#### 3. **CalculateCampaignAnalytics** (Job)
- **Frequência:** A cada hora
- **Função:** Calcula métricas de campanhas
- **Processo:**
  1. Identifica campanhas que precisam recálculo
  2. Calcula métricas de performance
  3. Cria snapshots diários
  4. Atualiza analytics consolidados

#### 4. **SyncCampaignStatusWithBooleans** (Job)
- **Frequência:** A cada 5 minutos
- **Função:** Sincroniza enum status com campos boolean
- **Processo:**
  1. Identifica inconsistências entre status enum e booleans
  2. Corrige automaticamente
  3. Mantém compatibilidade com código legado

#### 5. **ProcessMessageRetries** (Job)
- **Frequência:** A cada minuto
- **Função:** Processa tentativas de reenvio
- **Processo:**
  1. Identifica mensagens prontas para retry
  2. Aplica backoff exponencial
  3. Reenvia mensagens falhadas
  4. Atualiza contadores de tentativas

### Fluxo de Negócio Completo

#### 1. **Criação de Campanha**
```
1. Usuário cria template WhatsApp
2. Template é publicado na API WhatsApp
3. Usuário cria campanha associando template
4. Usuário adiciona clientes à campanha
5. Sistema valida dados e salva campanha
```

#### 2. **Lançamento de Campanha**
```
1. Usuário dispara lançamento da campanha
2. Sistema gera mensagens individuais para cada cliente
3. Sistema processa parâmetros dinâmicos ({{client.name}}, etc.)
4. Mensagens são marcadas como prontas para envio
5. Status da campanha muda para SENDING
```

#### 3. **Envio de Mensagens**
```
1. Cron job busca mensagens prontas (a cada minuto)
2. Para cada mensagem:
   - Gera payload WhatsApp
   - Envia via API
   - Atualiza status da mensagem
   - Registra logs
3. Sistema aplica rate limiting para evitar bloqueios
```

#### 4. **Sincronização de Status**
```
1. Job proativo identifica mensagens para verificação
2. Consulta WhatsApp API para status real
3. Compara com status local
4. Atualiza se houver diferença
5. Registra logs de sincronização
```

#### 5. **Cálculo de Analytics**
```
1. Job identifica campanhas que precisam recálculo
2. Calcula métricas:
   - Taxa de entrega
   - Taxa de leitura
   - Taxa de resposta
   - Taxa de falha
3. Gera insights automáticos
4. Cria snapshots históricos
```

### Regras de Negócio Importantes

#### Status de Campanhas
- **DRAFT:** Campanha criada, pode ser editada
- **SCHEDULED:** Agendada para envio futuro
- **SENDING:** Envio em andamento
- **COMPLETED:** Todas as mensagens processadas
- **FAILED:** Falha geral na campanha
- **CANCELLED:** Cancelada pelo usuário

#### Sistema de Retry
- Máximo 3 tentativas por mensagem
- Backoff exponencial: 5min, 15min, 1h
- Mensagens com 3 falhas são marcadas como permanentemente falhadas

#### Rate Limiting
- Máximo 500 mensagens por minuto por número
- Delay de 1 segundo a cada 10 mensagens no sync proativo
- Controle de overlapping nos jobs

#### Sincronização Proativa
- Verifica mensagens sem confirmação há mais de 30 minutos
- Reverifica mensagens não confirmadas a cada 2 horas
- Para após confirmação de entrega

### Monitoramento e Logs

#### DBLog Integration
Todos os processos críticos registram logs estruturados:
- Envio de mensagens
- Sincronizações
- Cálculos de analytics
- Erros e exceções

#### Métricas Disponíveis
- Taxa de sucesso de envio
- Tempo médio de entrega
- Taxa de sincronização
- Performance por campanha
- Engajamento por tipo de mensagem

### Considerações de Performance

#### Otimizações Implementadas
- Processamento em lotes
- Rate limiting inteligente
- Cache de templates
- Índices otimizados no banco
- Jobs com timeout e retry

#### Escalabilidade
- Suporte a múltiplas organizações
- Processamento assíncrono
- Separação de responsabilidades
- Arquitetura orientada a domínios

### Services WhatsApp Específicos

#### WhatsAppMessage Domain (`app/Services/Meta/WhatsApp/Domains/WhatsAppMessage.php`)
Domain específico para mensagens WhatsApp com integração à API.

**Propriedades Específicas:**
- `whatsapp_message_id` - ID retornado pela API WhatsApp
- `message_status` - Status específico do WhatsApp
- `wa_id` - ID do usuário WhatsApp
- `input_phone` - Telefone de entrada
- `messaging_product` - Produto de mensageria (whatsapp)
- `json` - Payload completo da API
- `webhook_entries` - Entradas de webhook recebidas
- `last_status_check` - Última verificação de status
- `status_check_count` - Contador de verificações
- `delivery_confirmed_at` - Confirmação de entrega
- `needs_status_check` - Flag para verificação necessária

**Métodos Específicos:**
```php
toWhatsAppTemplatePayload(): array    // Payload para template
toWhatsAppDirectPayload(): array      // Payload para mensagem direta
confirmDelivery(): void               // Confirma entrega
recordStatusCheck(bool $success): void // Registra verificação
needsStatusCheck(): bool              // Precisa verificação
```

#### WhatsAppMessageRepository (`app/Services/Meta/WhatsApp/Repositories/WhatsAppMessageRepository.php`)
Repository específico para operações WhatsApp.

**Métodos Específicos:**
```php
fetchByMessageId(int $message_id): ?WhatsAppMessage
fetchByWhatsAppId(string $whatsapp_id): ?WhatsAppMessage
fetchByExternalWamId(string $wam_id): ?WhatsAppMessage
fetchNeedingStatusCheck(int $limit = 100): array
markAsDelivered(string $whatsapp_id): bool
updateFromWebhook(array $webhook_data): void
```

#### MessageService (`app/Services/Meta/WhatsApp/MessageService.php`)
Service principal para envio de mensagens WhatsApp.

**Métodos Principais:**
```php
send(Message $message): array         // Envia mensagem
sendTemplate(Message $message): array // Envia template
sendDirectMessage(Message $message): array // Envia mensagem direta
validateTemplate(Template $template): bool // Valida template
processWebhook(array $data): void     // Processa webhook
```

**Integração com API:**
- Autenticação via token
- Rate limiting automático
- Retry logic integrado
- Validação de payloads
- Processamento de respostas

#### TemplateService (`app/Services/Meta/WhatsApp/TemplateService.php`)
Service para gestão de templates WhatsApp.

**Métodos Principais:**
```php
publish(Template $template): array    // Publica template
getStatus(string $template_id): array // Consulta status
delete(string $template_id): bool     // Remove template
validate(Template $template): bool    // Valida estrutura
```

### Webhook Processing

#### WhatsAppWebHookEntry Domain
Domain para processar entradas de webhook do WhatsApp.

**Propriedades:**
- `whatsapp_message_id` - ID da mensagem WhatsApp
- `entry_type` - Tipo de entrada (status, message)
- `status` - Status reportado
- `timestamp` - Timestamp do evento
- `metadata` - Dados adicionais
- `raw_json` - JSON completo recebido

**Métodos:**
```php
processStatusUpdate(): void           // Processa atualização de status
extractMessageData(): array          // Extrai dados da mensagem
isValidEntry(): bool                  // Valida entrada
```

### Integração com WhatsApp Business API

#### Endpoints Utilizados
```
POST /v17.0/{phone-number-id}/messages     # Envio de mensagens
GET  /v17.0/{message-id}                   # Consulta status
POST /v17.0/{business-account-id}/message_templates # Criar template
GET  /v17.0/{business-account-id}/message_templates # Listar templates
```

#### Autenticação
- **Token de Acesso:** Configurado por número de telefone
- **Webhook Token:** Para validação de webhooks
- **Business Account ID:** Identificação da conta

#### Rate Limits
- **Mensagens:** 1000/segundo por número
- **Templates:** 100/hora para criação
- **Consultas:** 200/segundo

### Fluxo de Webhook

#### Recebimento de Status
```
1. WhatsApp envia webhook para nossa URL
2. Sistema valida assinatura e token
3. Processa cada entrada do webhook
4. Atualiza status da mensagem local
5. Registra evento de engajamento
6. Atualiza métricas da campanha
7. Responde com 200 OK
```

#### Tipos de Status Recebidos
- **sent:** Mensagem enviada
- **delivered:** Mensagem entregue
- **read:** Mensagem lida
- **failed:** Falha na entrega

### Configurações Específicas

#### Variáveis de Ambiente
```env
WHATSAPP_API_URL=https://graph.facebook.com/v17.0
WHATSAPP_WEBHOOK_TOKEN=seu_webhook_token
WHATSAPP_VERIFY_TOKEN=seu_verify_token
WHATSAPP_DEFAULT_PHONE_NUMBER_ID=seu_phone_id
WHATSAPP_BUSINESS_ACCOUNT_ID=seu_business_id
```

#### Configurações por Número
Cada número de telefone pode ter configurações específicas:
- Token de acesso próprio
- Rate limits customizados
- Webhook URL específica
- Templates associados

Esta documentação cobre todos os aspectos técnicos e funcionais do sistema de campanhas WhatsApp, servindo como referência completa para desenvolvimento, manutenção e evolução do sistema.
