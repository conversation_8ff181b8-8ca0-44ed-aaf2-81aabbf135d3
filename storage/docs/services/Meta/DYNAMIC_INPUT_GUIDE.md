# Dynamic Input Processing System - Complete Guide

## 🎯 **Overview**

The Dynamic Input Processing System allows you to collect user input and automatically update domain objects (Client, PhoneNumber, Organization) in real-time during WhatsApp conversations.

## 🏗️ **Core Concepts**

### **1. Input Steps**
- Steps with `is_input = true` wait for user text input
- Use `input_field` pattern to specify what to update: `"client.name"`, `"client.email"`
- Automatic validation and error handling

### **2. Field Patterns**
- **Format**: `"model.field"` (e.g., `"client.name"`, `"client.email"`)
- **Supported Models**: `client`, `phone_number`, `organization`
- **Dynamic Updates**: Changes are saved immediately to database

### **3. Validation System**
- **Built-in validation**: required, min_length, max_length, pattern
- **Field-specific validation**: email format, phone format, CPF, CEP
- **Custom error messages**: User-friendly validation feedback

## 📋 **Implementation Guide**

### **1. Create Input Step**
```php
Step::create([
    'step' => 'name_input',
    'is_input' => true,  // ← Makes it an input step
    'json' => json_encode([
        'message' => 'What\'s your full name?',
        'input_field' => 'client.name',  // ← What to update
        'required' => true,
        'min_length' => 2,
        'max_length' => 100
    ])
]);
```

### **2. Supported Input Fields**

#### **Client Fields**
- `client.name` - Client's full name
- `client.email` - Email address (with email validation)
- `client.phone` - Phone number (with phone validation)
- `client.profession` - Client's profession
- `client.cpf` - CPF number (with CPF validation)
- `client.address` - Full address
- `client.neighborhood` - Neighborhood
- `client.cep` - Postal code (with CEP validation)
- `client.complement` - Address complement

#### **Phone Number Fields**
- `phone_number.name` - Phone number label
- `phone_number.description` - Phone number description

### **3. Validation Options**
```json
{
  "input_field": "client.email",
  "required": true,
  "min_length": 5,
  "max_length": 100,
  "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
  "validation": "required|email|max:100"
}
```

## 🚀 **Usage Examples**

### **Example 1: Client Registration Flow**
```php
// Step 1: Name Input
Step::create([
    'step' => 'name_input',
    'is_input' => true,
    'json' => json_encode([
        'message' => '📝 What\'s your full name?',
        'input_field' => 'client.name',
        'required' => true,
        'min_length' => 2
    ])
]);

// Step 2: Email Input
Step::create([
    'step' => 'email_input',
    'is_input' => true,
    'json' => json_encode([
        'message' => '📧 Please enter your email address:',
        'input_field' => 'client.email',
        'required' => true
    ])
]);

// Step 3: Phone Input
Step::create([
    'step' => 'phone_input',
    'is_input' => true,
    'json' => json_encode([
        'message' => '📱 Enter your phone number:',
        'input_field' => 'client.phone',
        'required' => true,
        'min_length' => 10
    ])
]);
```

### **Example 2: Address Collection**
```php
Step::create([
    'step' => 'address_input',
    'is_input' => true,
    'json' => json_encode([
        'message' => '🏠 Please enter your full address:',
        'input_field' => 'client.address',
        'required' => true,
        'min_length' => 10,
        'max_length' => 200
    ])
]);
```

### **Example 3: Professional Information**
```php
Step::create([
    'step' => 'profession_input',
    'is_input' => true,
    'json' => json_encode([
        'message' => '💼 What\'s your profession?',
        'input_field' => 'client.profession',
        'required' => false,
        'max_length' => 50
    ])
]);
```

## 🔄 **Processing Flow**

### **1. User Input Received**
```
User sends: "John Doe"
↓
System detects input step
↓
Validates input against rules
↓
Updates client.name = "John Doe"
↓
Saves to database
↓
Sends confirmation: "✅ Got it! Your name has been saved."
↓
Moves to next step
```

### **2. Validation Failure**
```
User sends: "Jo"
↓
System validates (min_length: 5)
↓
Validation fails
↓
Sends error: "❌ Minimum length is 5 characters. Please try again."
↓
Stays on same step
```

### **3. Critical Error**
```
Database update fails
↓
System logs error
↓
Sends error: "❌ Failed to save your information. Please try again."
↓
Finishes conversation
```

## 🧪 **Testing & Validation**

### **Create Example Flow**
```bash
# Create client registration flow
php artisan whatsapp:create-input-example 1
```

### **Test Input Processing**
```bash
# Test the dynamic input service
php artisan whatsapp:test-input
```

### **Test Real Flow**
1. Assign flow to a phone number
2. Send WhatsApp message to start flow
3. Follow prompts to enter information
4. Check database to see updates in real-time

## 🔍 **Built-in Validations**

### **Email Validation**
- Validates proper email format
- Example: `<EMAIL>` ✅, `invalid-email` ❌

### **Phone Validation**
- Removes non-digits and validates length (10-15 digits)
- Example: `11999887766` ✅, `123` ❌

### **CPF Validation**
- Validates 11-digit CPF format
- Example: `12345678901` ✅, `123456` ❌

### **CEP Validation**
- Validates 8-digit postal code format
- Example: `12345678` ✅, `1234` ❌

## 🛠️ **Advanced Features**

### **Custom Validation Patterns**
```json
{
  "input_field": "client.cpf",
  "pattern": "^[0-9]{11}$",
  "required": true
}
```

### **Conditional Required Fields**
```json
{
  "input_field": "client.email",
  "required": true,
  "validation": "required|email"
}
```

### **Length Constraints**
```json
{
  "input_field": "client.name",
  "min_length": 2,
  "max_length": 100,
  "required": true
}
```

## 🚨 **Error Handling**

### **Validation Errors**
- User gets friendly error message
- Conversation stays on same step
- User can retry input

### **Database Errors**
- System logs detailed error
- User gets generic error message
- Conversation is finished to prevent data corruption

### **Configuration Errors**
- Invalid `input_field` patterns are caught
- System logs configuration issues
- Graceful fallback behavior

## 📊 **Best Practices**

### **1. Use Clear Messages**
```php
✅ Good: "📝 What's your full name?"
❌ Avoid: "Enter name"
```

### **2. Provide Context**
```php
✅ Good: "📧 Please enter your email address (we'll use this for order confirmations):"
❌ Avoid: "Email?"
```

### **3. Set Appropriate Validation**
```php
✅ Good: min_length: 2, max_length: 100 for names
❌ Avoid: No validation or overly strict validation
```

### **4. Use Confirmation Messages**
```php
// System automatically sends: "✅ Got it! Your name has been saved."
```

### **5. Handle Optional Fields**
```php
{
  "input_field": "client.profession",
  "required": false,  // ← Make optional fields explicit
  "message": "💼 What's your profession? (optional)"
}
```

## 🎯 **Integration with Other Features**

### **Works with Variable Substitution**
```php
// After collecting name, use it in messages
"message": "Thank you {{client.name}}! Now let's get your email..."
```

### **Works with Conditional Navigation**
```php
// Can branch based on collected data
// Use conditional buttons after input collection
```

### **Works with All Step Types**
```php
// Input steps can be mixed with message, interactive, and command steps
```

## 🔧 **Troubleshooting**

### **Input Not Being Processed**
- Check `is_input = true` is set
- Verify `input_field` format is correct
- Check step JSON configuration

### **Validation Always Failing**
- Review validation rules in step JSON
- Check field-specific validation requirements
- Test with simple input first

### **Database Not Updating**
- Check repository methods are working
- Verify client/model exists in conversation
- Check application logs for errors

## 🎉 **What's Possible Now**

With dynamic input processing, you can create:

- **Registration flows** - Collect complete user profiles
- **Order forms** - Gather delivery information
- **Support tickets** - Collect issue details
- **Surveys** - Gather user feedback
- **Profile updates** - Allow users to update their information
- **KYC processes** - Collect identity verification data

The system automatically handles:
- ✅ Input validation and error messages
- ✅ Database updates via repositories
- ✅ Error handling and recovery
- ✅ User-friendly feedback
- ✅ Integration with variable substitution
- ✅ Seamless flow continuation

## 🚀 **Next Steps**

1. **Create your first input flow**
2. **Test with real WhatsApp conversations**
3. **Monitor validation and error rates**
4. **Optimize user experience based on feedback**
5. **Extend to custom domain objects as needed**

The dynamic input system is production-ready and integrates seamlessly with all other ChatBot features!
