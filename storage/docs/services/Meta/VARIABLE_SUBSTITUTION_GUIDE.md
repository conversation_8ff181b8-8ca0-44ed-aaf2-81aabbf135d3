# Variable Substitution System - Complete Guide

## 🎯 **Overview**

The Variable Substitution System allows you to use dynamic variables in your WhatsApp ChatBot messages using the `{{model.variable}}` pattern. Variables are automatically replaced with actual values from your domain objects.

## 📝 **Syntax**

Use the pattern: `{{model.variable}}`

- `model`: The domain object name (client, phone_number, organization, etc.)
- `variable`: The property/field name from that object

## 🏗️ **Available Models**

### **Client Variables**
- `{{client.id}}` - Client ID
- `{{client.name}}` - Client name
- `{{client.phone}}` - Client phone number
- `{{client.email}}` - Client email
- `{{client.profession}}` - Client profession
- `{{client.address}}` - Client address
- `{{client.neighborhood}}` - Client neighborhood
- `{{client.cep}}` - Client postal code
- `{{client.description}}` - Client description

### **Phone Number Variables**
- `{{phone_number.id}}` - Phone number ID
- `{{phone_number.phone_number}}` - The actual phone number
- `{{phone_number.name}}` - Phone number name/label
- `{{phone_number.description}}` - Phone number description

### **Organization Variables**
- `{{organization.id}}` - Organization ID
- `{{organization.name}}` - Organization name
- `{{organization.description}}` - Organization description

### **Campaign Variables** (when available)
- `{{campaign.id}}` - Campaign ID
- `{{campaign.name}}` - Campaign name
- `{{campaign.description}}` - Campaign description

### **Template Variables** (when available)
- `{{template.id}}` - Template ID
- `{{template.name}}` - Template name
- `{{template.language}}` - Template language

## 🚀 **Usage Examples**

### **Basic Welcome Message**
```
Hello {{client.name}}! 👋

Welcome to {{organization.name}}. 
Your phone number is {{client.phone}}.
```

### **Order Confirmation**
```
🎉 Order Confirmed!

Dear {{client.name}},

Your order has been successfully placed!

📱 Contact: {{client.phone}}
🏢 Company: {{organization.name}}
📞 Support: {{phone_number.phone_number}}

Thank you for choosing {{organization.name}}!
```

### **Personalized Service Message**
```
Hi {{client.name}}! 

We see you're from {{client.neighborhood}}.
Our {{organization.name}} team is ready to help!

Contact us at {{phone_number.phone_number}} anytime.
```

## 🛠️ **Implementation**

### **1. In Step JSON Configuration**
```json
{
  "message": "Hello {{client.name}}! Welcome to {{organization.name}}.",
  "component_type": "text"
}
```

### **2. In Component Text**
```json
{
  "text": "Dear {{client.name}}, your order from {{organization.name}} is ready!",
  "type": "BODY"
}
```

### **3. In Button Text**
```json
{
  "text": "Contact {{client.name}}",
  "type": "QUICK_REPLY"
}
```

## 🧪 **Testing**

### **Test Variable Substitution**
```bash
# Test the substitution service
php artisan whatsapp:test-variables
```

### **Create Example Flow**
```bash
# Create a flow demonstrating variable substitution
php artisan whatsapp:create-variable-example 1
```

## 🔧 **Advanced Features**

### **Variable Validation**
The system validates variables before sending messages:
- Checks if the model exists
- Verifies the variable/property exists
- Returns original `{{model.variable}}` if not found

### **Fallback Behavior**
- If model is not available: keeps original `{{model.variable}}`
- If variable doesn't exist: keeps original `{{model.variable}}`
- If value is null: returns empty string
- If value is boolean: returns 'true' or 'false'
- If value is array/object: returns JSON representation

### **Date Formatting**
Carbon dates are automatically formatted as `Y-m-d H:i:s`:
```
Created: {{client.created_at}}
// Output: Created: 2024-01-15 14:30:00
```

## 📋 **Step-by-Step Setup**

### **1. Create Flow with Variables**
```php
Step::create([
    'json' => json_encode([
        'message' => 'Hello {{client.name}}! Welcome to {{organization.name}}.'
    ])
]);
```

### **2. The System Automatically:**
- Detects available models (client, phone_number, etc.)
- Extracts variables from message text
- Replaces variables with actual values
- Sends processed message to WhatsApp

### **3. No Additional Configuration Needed!**
The ChatBot service automatically handles variable substitution for:
- Text messages
- Interactive messages
- Template messages
- Button text
- Component text

## 🎯 **Best Practices**

### **1. Use Descriptive Variables**
```
✅ Good: "Hello {{client.name}}, your order #{{order.id}} is ready!"
❌ Avoid: "Hello {{client.name}}, your {{thing}} is {{status}}!"
```

### **2. Provide Fallbacks**
```
✅ Good: "Hello {{client.name}}! Welcome to our service."
✅ Fallback: If client.name is empty, shows "Hello ! Welcome to our service."
```

### **3. Test Variables**
Always test your flows with real data to ensure variables work correctly.

### **4. Keep Messages Natural**
```
✅ Good: "Hi {{client.name}}! Your order is ready for pickup."
❌ Robotic: "CLIENT_NAME={{client.name}} ORDER_STATUS=READY"
```

## 🔍 **Debugging**

### **Check Available Variables**
```php
$substitutionService = new VariableSubstitutionService();
$variables = $substitutionService->getVariablesFromText($messageText);
```

### **Validate Variables**
```php
$errors = $substitutionService->validateVariables($messageText, $availableModels);
```

### **Log Variable Substitution**
The system logs variable substitution in the ChatBot processing logs.

## 🚨 **Common Issues**

### **Variable Not Replaced**
- Check if the model is available in the context
- Verify the variable name matches the domain property
- Ensure the model is not null

### **Empty Values**
- Variables with null values return empty strings
- Check your domain object has the expected data

### **Wrong Model Name**
- Use exact model names: `client`, `phone_number`, `organization`
- Model names are case-sensitive

## 🎉 **What's Next?**

The variable substitution system is fully functional and ready for production use. Future enhancements could include:

- Custom formatters for dates, numbers, etc.
- Conditional variables: `{{client.name || 'Guest'}}`
- Nested object access: `{{client.organization.name}}`
- Mathematical operations: `{{order.total * 1.1}}`

## 📞 **Support**

The variable substitution system is integrated into:
- ✅ ChatBot message processing
- ✅ WhatsApp message sending
- ✅ Template message generation
- ✅ Interactive message creation
- ✅ Step processing logic

Everything works automatically - just use `{{model.variable}}` in your messages!
