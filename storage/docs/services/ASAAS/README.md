# Serviço ASAAS - Documentação de Negócio

## O que é o Serviço ASAAS?

O serviço ASAAS é nossa integração com o gateway de pagamentos brasileiro ASAAS, que permite que organizações do nosso sistema tenham capacidades completas de cobrança e gestão financeira. É como se cada organização tivesse sua própria "conta bancária digital" para receber pagamentos.

## Funcionalidades Disponíveis

### ✅ O que o serviço PODE fazer hoje:

#### 1. **Gestão de Organizações**
- **Criação automática de subconta ASAAS**: Quando uma nova organização é criada no sistema, automaticamente criamos uma subconta no ASAAS para ela
- **Controle de acesso por assinatura**: O sistema verifica se a organização tem direito de usar o sistema baseado no status da assinatura ASAAS
- **Período de cortesia**: Organizações podem usar o sistema gratuitamente por um período determinado
- **Verificação de status de assinatura**: Consulta automática do status de pagamento da assinatura da organização

#### 2. **Gestão de Clientes**
- **Cadastro automático no ASAAS**: Quando um cliente é criado no sistema, automaticamente criamos um cliente correspondente no ASAAS
- **Sincronização de dados**: Informações como nome, email, telefone, CPF/CNPJ e endereço são sincronizadas automaticamente
- **Validação de documentos**: O sistema valida CPF/CNPJ antes de enviar para o ASAAS

#### 3. **Gestão de Vendas e Cobranças**
- **Geração automática de cobranças**: Quando uma venda é finalizada, automaticamente gera uma cobrança no ASAAS
- **Múltiplas formas de pagamento**: Suporte a boleto bancário, PIX, cartão de crédito
- **Sincronização de status**: O sistema monitora automaticamente o status dos pagamentos (pendente, pago, vencido, cancelado)
- **URLs de pagamento**: Geração automática de links para pagamento online

#### 4. **Monitoramento e Relatórios**
- **Consulta de status específico**: Usuários da organização podem consultar o status de uma venda específica
- **Logs detalhados**: Todas as operações com ASAAS são registradas para auditoria
- **Relatórios de erro**: Sistema identifica e reporta problemas de sincronização

#### 5. **Automações**
- **Sincronização automática**: Cronjobs que rodam a cada 30 minutos para atualizar status de pagamentos
- **Limpeza de logs**: Processo automático de limpeza de logs antigos
- **Retry automático**: Sistema tenta novamente operações que falharam

### 🔄 Fluxos de Negócio Implementados

#### Fluxo de Nova Organização
1. Organização é criada no sistema
2. Sistema cria subconta no ASAAS automaticamente
3. Organização recebe período de cortesia (ex: 30 dias grátis)
4. Após cortesia, precisa pagar assinatura para continuar usando

#### Fluxo de Venda Completa
1. Cliente é cadastrado (se não existir, cria no ASAAS automaticamente)
2. Venda é registrada no sistema
3. Cobrança é gerada automaticamente no ASAAS
4. Cliente recebe link/boleto para pagamento
5. Sistema monitora status do pagamento automaticamente
6. Quando pago, status é atualizado no sistema

#### Fluxo de Controle de Acesso
1. Usuário tenta acessar funcionalidades do sistema
2. Sistema verifica se organização tem direito de acesso
3. Verifica se está em cortesia OU se assinatura está ativa
4. Libera ou bloqueia acesso conforme status

## Benefícios para o Negócio

### Para Organizações (Clientes do Sistema)
- **Cobrança profissional**: Recebem sistema completo de cobrança sem precisar contratar ASAAS diretamente
- **Gestão centralizada**: Todos os clientes e vendas ficam organizados em um só lugar
- **Múltiplas formas de pagamento**: Podem oferecer boleto, PIX e cartão para seus clientes
- **Acompanhamento em tempo real**: Veem status de pagamentos atualizados automaticamente

### Para Nossa Empresa
- **Receita recorrente**: Cobramos assinatura mensal das organizações
- **Escalabilidade**: Cada organização tem sua própria subconta ASAAS
- **Controle de acesso**: Podemos bloquear organizações inadimplentes automaticamente
- **Auditoria completa**: Todos os pagamentos ficam registrados e rastreáveis

## Limitações Atuais

### ❌ O que o serviço NÃO pode fazer (mas seria interessante):

#### 1. **Assinaturas Recorrentes Automáticas**
- **Limitação atual**: Só criamos cobranças avulsas
- **Seria interessante**: Produtos com cobrança recorrente automática (mensalidades, anuidades)
- **Impacto**: Organizações que vendem serviços recorrentes precisam criar cobranças manualmente

#### 2. **Split de Pagamentos**
- **Limitação atual**: Todo pagamento vai direto para a organização
- **Seria interessante**: Dividir automaticamente uma porcentagem para nossa empresa
- **Impacto**: Precisamos cobrar nossa taxa separadamente

#### 3. **Gestão de Estorno e Reembolsos**
- **Limitação atual**: Não temos interface para estornos
- **Seria interessante**: Permitir estornos direto pelo sistema
- **Impacto**: Organizações precisam acessar ASAAS diretamente para estornos

#### 4. **Relatórios Financeiros Avançados**
- **Limitação atual**: Relatórios básicos de status
- **Seria interessante**: Dashboards com gráficos de receita, inadimplência, etc.
- **Impacto**: Organizações não têm visão analítica dos pagamentos

#### 5. **Integração com Contabilidade**
- **Limitação atual**: Dados ficam isolados no sistema
- **Seria interessante**: Exportar dados para sistemas contábeis
- **Impacto**: Organizações precisam fazer conciliação manual

#### 6. **Notificações Personalizadas**
- **Limitação atual**: Notificações padrão do ASAAS
- **Seria interessante**: Personalizar emails e SMS de cobrança
- **Impacto**: Comunicação com clientes finais não tem identidade visual da organização

#### 7. **Marketplace de Pagamentos**
- **Limitação atual**: Uma organização = uma subconta
- **Seria interessante**: Permitir que organizações vendam produtos de outras organizações
- **Impacto**: Não conseguimos atender modelos de marketplace

#### 8. **Análise de Risco**
- **Limitação atual**: Não analisamos padrões de inadimplência
- **Seria interessante**: Alertas automáticos para clientes com alto risco
- **Impacto**: Organizações não têm insights para reduzir inadimplência

## Roadmap Futuro

### Prioridade Alta (próximos 3 meses)
1. **Assinaturas recorrentes** - Permitir produtos com cobrança automática mensal/anual
2. **Relatórios financeiros** - Dashboard com métricas de receita e inadimplência
3. **Gestão de estornos** - Interface para processar reembolsos

### Prioridade Média (próximos 6 meses)
1. **Split de pagamentos** - Cobrança automática de nossa taxa
2. **Notificações personalizadas** - Customização de emails de cobrança
3. **Integração contábil** - Exportação de dados para sistemas contábeis

### Prioridade Baixa (futuro)
1. **Marketplace** - Suporte a vendas entre organizações
2. **Análise de risco** - IA para predição de inadimplência
3. **API pública** - Permitir integrações externas

## Métricas de Sucesso

### Métricas Técnicas
- **Uptime da integração**: > 99.5%
- **Tempo de sincronização**: < 5 minutos
- **Taxa de erro**: < 1%

### Métricas de Negócio
- **Organizações ativas**: Número de organizações usando ASAAS
- **Volume de transações**: Valor total processado mensalmente
- **Taxa de conversão**: % de cobranças que são pagas
- **Receita recorrente**: Valor mensal de assinaturas

## Suporte e Troubleshooting

### Problemas Comuns
1. **Organização bloqueada**: Verificar status da assinatura
2. **Pagamento não sincronizado**: Executar comando de sincronização manual
3. **Cliente não criado no ASAAS**: Verificar dados obrigatórios (CPF/CNPJ)

### Comandos Úteis
- `php artisan asaas:sync-payments` - Sincronizar status de pagamentos
- `php artisan asaas:process-logs` - Processar e limpar logs
- `php artisan asaas:sync-payments create-customers` - Criar clientes pendentes

### Logs Importantes
- `storage/logs/asaas.log` - Logs específicos do ASAAS
- Tabela `asaas_logs` - Histórico completo de operações
