# Epic 2 - Criar Entidade AsaasSale (Payment no Asaas)

## 📋 **RESUMO**

Criação da entidade completa AsaasSale que representa um payment do Asaas no nosso sistema. Esta entidade será linkada a uma Sale existente e permitirá controle completo do ciclo de vida de pagamentos via API do Asaas.

## 🎯 **OBJETIVOS**

- Criar migration para tabela `asaas_sales`
- Implementar Model `AsaasSale` com relacionamentos
- Desenvolver Factory para criação de instâncias
- Criar Domain `AsaasSale` com regras de negócio
- Implementar Repository para acesso a dados
- Desenvolver Use Cases principais (Create, Update, Sync, Cancel, Refund)

## 📊 **ESTRUTURA DA ENTIDADE**

### **Campos da Tabela `asaas_sales`**

```sql
CREATE TABLE asaas_sales (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sale_id BIGINT NOT NULL,
    organization_id BIGINT NOT NULL,
    client_id BIGINT NOT NULL,
    asaas_payment_id VARCHAR(255) UNIQUE NOT NULL,
    asaas_customer_id VARCHAR(255) NOT NULL,
    
    -- Payment Details
    value DECIMAL(10,2) NOT NULL,
    net_value DECIMAL(10,2) NULL,
    original_value DECIMAL(10,2) NULL,
    interest_value DECIMAL(10,2) NULL,
    discount_value DECIMAL(10,2) NULL,
    description TEXT NULL,
    
    -- Payment Configuration
    billing_type ENUM('BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED') NOT NULL,
    due_date DATE NOT NULL,
    payment_date DATE NULL,
    original_due_date DATE NULL,
    client_payment_date DATE NULL,
    
    -- Status Control
    status ENUM(
        'PENDING', 'PENDING_CONFIRMATION', 'CONFIRMED', 'RECEIVED', 
        'OVERDUE', 'REFUNDED', 'RECEIVED_IN_CASH', 'REFUND_REQUESTED',
        'REFUND_IN_PROGRESS', 'CHARGEBACK_REQUESTED', 'CHARGEBACK_DISPUTE',
        'AWAITING_CHARGEBACK_REVERSAL', 'DUNNING_REQUESTED', 'DUNNING_RECEIVED',
        'AWAITING_RISK_ANALYSIS'
    ) DEFAULT 'PENDING',
    
    -- URLs and References
    invoice_url TEXT NULL,
    invoice_number VARCHAR(50) NULL,
    bank_slip_url TEXT NULL,
    pix_qr_code_id VARCHAR(255) NULL,
    external_reference VARCHAR(255) NULL,
    
    -- Installment Information
    installment_id VARCHAR(255) NULL,
    installment_count INTEGER NULL,
    installment_value DECIMAL(10,2) NULL,
    installment_number INTEGER NULL,
    
    -- Credit Information
    credit_date DATE NULL,
    estimated_credit_date DATE NULL,
    anticipated BOOLEAN DEFAULT FALSE,
    anticipable BOOLEAN DEFAULT FALSE,
    
    -- Additional Data
    can_be_paid_after_due_date BOOLEAN DEFAULT TRUE,
    deleted BOOLEAN DEFAULT FALSE,
    nosso_numero VARCHAR(50) NULL,
    
    -- Sync Control
    asaas_synced_at TIMESTAMP NULL,
    asaas_sync_errors JSON NULL,
    sync_status ENUM('pending', 'synced', 'error') DEFAULT 'pending',
    asaas_webhook_data JSON NULL,
    
    -- Financial Details (JSON)
    discount_config JSON NULL,
    fine_config JSON NULL,
    interest_config JSON NULL,
    split_config JSON NULL,
    credit_card_data JSON NULL,
    chargeback_data JSON NULL,
    escrow_data JSON NULL,
    refunds_data JSON NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    
    INDEX idx_asaas_payment_id (asaas_payment_id),
    INDEX idx_sale_id (sale_id),
    INDEX idx_client_id (client_id),
    INDEX idx_organization_id (organization_id),
    INDEX idx_status (status),
    INDEX idx_billing_type (billing_type),
    INDEX idx_due_date (due_date),
    INDEX idx_payment_date (payment_date),
    INDEX idx_sync_status (sync_status),
    INDEX idx_asaas_synced_at (asaas_synced_at)
);
```

## 🏗️ **ARQUITETURA**

### **1. Migration**
```
database/migrations/xxxx_create_asaas_sales_table.php
```

### **2. Model**
```
app/Services/ASAAS/Models/AsaasSale.php
```
- Relacionamentos: `belongsTo(Sale::class)`, `belongsTo(Client::class)`, `belongsTo(Organization::class)`
- Casts para JSON, decimal e boolean
- Scopes para status e filtros

### **3. Domain**
```
app/Services/ASAAS/Domains/AsaasSale.php
```
- Validações de negócio
- Métodos de transformação para API
- Controle de status e lifecycle

### **4. Factory**
```
app/Services/ASAAS/Factories/AsaasSaleFactory.php
```
- `buildFromModel()`
- `buildFromAsaasResponse()`
- `buildFromSale()`

### **5. Repository**
```
app/Services/ASAAS/Repositories/AsaasSaleRepository.php
```
- CRUD operations
- Métodos de busca por status
- Controle de sincronização

### **6. Use Cases**
```
app/Services/ASAAS/UseCases/Sales/
├── CreateAsaasPayment.php
├── UpdateAsaasPayment.php
├── SyncAsaasPayment.php
├── CancelAsaasPayment.php
├── RefundAsaasPayment.php
├── GetAsaasPaymentStatus.php
└── ProcessWebhookPayment.php
```

## 📝 **EXEMPLO DE PAYLOAD ASAAS**

### **Request Body (Criação)**
```json
{
    "customer": "cus_G7Dvo4iphUNk",
    "billingType": "BOLETO",
    "value": 129.9,
    "dueDate": "2017-06-10",
    "description": "Pedido 056984",
    "daysAfterDueDateToRegistrationCancellation": 1,
    "externalReference": "056984",
    "installmentCount": null,
    "totalValue": null,
    "installmentValue": null,
    "discount": {
        "value": 10,
        "dueDateLimitDays": 0,
        "type": "PERCENTAGE"
    },
    "interest": {"value": null},
    "fine": {"value": null, "type": "FIXED"},
    "postalService": false,
    "split": [{
        "walletId": null,
        "fixedValue": null,
        "percentualValue": null,
        "totalFixedValue": null,
        "externalReference": null,
        "description": null
    }]
}
```

### **Response (Asaas API)**
```json
{
    "object": "payment",
    "id": "pay_************",
    "dateCreated": "2017-03-10",
    "customer": "cus_G7Dvo4iphUNk",
    "subscription": null,
    "installment": null,
    "value": 129.9,
    "netValue": 124.9,
    "originalValue": null,
    "interestValue": null,
    "description": "Pedido 056984",
    "billingType": "BOLETO",
    "status": "PENDING",
    "dueDate": "2017-06-10",
    "originalDueDate": "2017-06-10",
    "paymentDate": null,
    "clientPaymentDate": null,
    "installmentNumber": null,
    "invoiceUrl": "https://www.asaas.com/i/************",
    "invoiceNumber": "********",
    "externalReference": "056984",
    "deleted": false,
    "anticipated": false,
    "anticipable": false,
    "creditDate": "2017-06-10",
    "estimatedCreditDate": "2017-06-10",
    "nossoNumero": "6453",
    "bankSlipUrl": "https://www.asaas.com/b/pdf/************",
    "discount": {
        "value": 10,
        "dueDateLimitDays": 0,
        "type": "PERCENTAGE"
    },
    "fine": {"value": 1},
    "interest": {"value": 2}
}
```

## 🔄 **USE CASES PRINCIPAIS**

### **1. CreateAsaasPayment**
- Valida se Sale não possui AsaasSale
- Verifica se Client possui AsaasClient
- Cria payment na API Asaas
- Persiste AsaasSale no banco
- Atualiza Sale com status de pagamento

### **2. UpdateAsaasPayment**
- Atualiza dados do payment no Asaas
- Sincroniza alterações locais
- Controla mudanças de status

### **3. SyncAsaasPayment**
- Busca status atual do payment
- Atualiza dados locais
- Processa mudanças de status
- Dispara eventos de negócio

### **4. CancelAsaasPayment**
- Cancela payment no Asaas
- Atualiza status local
- Reverte Sale se necessário

### **5. RefundAsaasPayment**
- Processa estorno no Asaas
- Atualiza valores e status
- Registra histórico de refund

### **6. ProcessWebhookPayment**
- Processa webhooks do Asaas
- Atualiza status em tempo real
- Dispara notificações

## 🧪 **TESTES**

### **Estrutura de Testes**
```
tests/Feature/Services/ASAAS/Sales/
├── CreateAsaasPaymentTest.php
├── UpdateAsaasPaymentTest.php
├── SyncAsaasPaymentTest.php
├── CancelAsaasPaymentTest.php
├── RefundAsaasPaymentTest.php
└── ProcessWebhookPaymentTest.php

tests/Unit/Services/ASAAS/Domains/AsaasSaleTest.php
tests/Unit/Services/ASAAS/Repositories/AsaasSaleRepositoryTest.php
```

### **Cenários de Teste**
- Criação de payments por tipo (BOLETO, PIX, CREDIT_CARD)
- Processamento de webhooks
- Mudanças de status
- Estornos e cancelamentos
- Sincronização bidirecional

## 📦 **ARQUIVOS DO PR**

### **Novos Arquivos**
```
database/migrations/xxxx_create_asaas_sales_table.php
app/Services/ASAAS/Models/AsaasSale.php
app/Services/ASAAS/Domains/AsaasSale.php
app/Services/ASAAS/Factories/AsaasSaleFactory.php
app/Services/ASAAS/Repositories/AsaasSaleRepository.php
app/Services/ASAAS/UseCases/Sales/CreateAsaasPayment.php
app/Services/ASAAS/UseCases/Sales/UpdateAsaasPayment.php
app/Services/ASAAS/UseCases/Sales/SyncAsaasPayment.php
app/Services/ASAAS/UseCases/Sales/CancelAsaasPayment.php
app/Services/ASAAS/UseCases/Sales/RefundAsaasPayment.php
app/Services/ASAAS/UseCases/Sales/GetAsaasPaymentStatus.php
app/Services/ASAAS/UseCases/Sales/ProcessWebhookPayment.php
database/factories/AsaasSaleFactory.php
```

### **Arquivos Modificados**
```
app/Models/Sale.php (adicionar relacionamento hasOne AsaasSale)
app/Domains/Inventory/Sale.php (adicionar métodos Asaas)
```

## ⚡ **INTEGRAÇÃO COM SISTEMA EXISTENTE**

### **Relacionamentos**
- `AsaasSale belongsTo Sale`
- `AsaasSale belongsTo Client`
- `AsaasSale belongsTo Organization`
- `Sale hasOne AsaasSale`

### **Hooks de Sincronização**
- Trigger automático na criação de Sale
- Webhook handling para updates em tempo real
- Jobs de sincronização em background
- Retry automático para falhas

## 🔒 **VALIDAÇÕES E REGRAS**

### **Validações de Negócio**
- Sale deve pertencer à Organization
- Client deve ter AsaasClient válido
- Valor deve ser positivo
- Data de vencimento não pode ser passada
- Não permitir duplicação de asaas_payment_id

### **Controle de Status**
- Transições de status válidas
- Logs de mudanças de status
- Notificações automáticas
- Rollback em caso de erro

## 📈 **MÉTRICAS E MONITORAMENTO**

### **Dashboards**
- Taxa de conversão por tipo de pagamento
- Tempo médio de confirmação
- Volume de estornos
- Performance de webhooks

### **Alertas**
- Falhas de sincronização
- Webhooks perdidos
- Pagamentos em atraso
- Erros de API
