# 🎯 **EPIC 3: Subscriptions Entity**

## 📋 **RESUMO**

Implementação completa da entidade `Subscriptions` para controlar assinaturas das organizações no nosso sistema. Esta entidade será responsável por gerenciar acesso, cortesias, testes gratuitos e integração com AsaasSubscriptions quando necessário.

## 🎯 **OBJETIVOS**

### **Objetivo Principal**
Criar uma entidade completa para controlar assinaturas das organizações, incluindo cortesias e integração opcional com ASAAS.

### **Objetivos Específicos**
1. ✅ Criação completa da entidade Subscriptions (migration, model, domain, factory, repository)
2. ✅ Implementação de 5 Use Cases padrão (Store, Update, Delete, Get, GetAll)
3. ✅ Sistema de cortesias independente do ASAAS
4. ✅ Integração opcional com AsaasSubscriptions
5. ✅ Controle de acesso baseado em status da assinatura

## 🏗️ **ARQUITETURA**

### **1. Conceito da Entidade**

```
Subscriptions:
- Entidade principal de controle de assinaturas
- Linkada diretamente à Organization
- Pode existir independente do ASAAS (cortesias/testes)
- Controla acesso ao sistema
- Integração opcional com AsaasSubscriptions

Fluxos:
1. Cortesia/Teste → Subscription sem AsaasSubscription
2. Assinatura Paga → Subscription + AsaasSubscription
3. Migração → Cortesia para Assinatura Paga
```

### **2. Estrutura de Arquivos**

```
database/migrations/
├── xxxx_create_subscriptions_table.php

app/Models/
├── Subscription.php

app/Domains/
├── Subscription.php

app/Factories/
├── SubscriptionFactory.php

app/Repositories/
├── SubscriptionRepository.php

app/UseCases/Subscriptions/
├── StoreSubscription.php
├── UpdateSubscription.php
├── DeleteSubscription.php
├── GetSubscription.php
├── GetAllSubscriptions.php
```

## 📊 **ESTRUTURA DE DADOS**

### **Migration: subscriptions**

```sql
CREATE TABLE subscriptions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Relacionamentos
    organization_id BIGINT UNSIGNED NOT NULL UNIQUE,
    
    -- Dados da Assinatura
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'CANCELLED', 'EXPIRED') DEFAULT 'ACTIVE',
    billing_type ENUM('BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED') DEFAULT 'BOLETO',
    cycle ENUM('MONTHLY', 'QUARTERLY', 'SEMIANNUAL', 'YEARLY') DEFAULT 'MONTHLY',
    value DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    
    -- Datas de Controle
    started_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NULL,
    next_due_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    
    -- Cortesia/Teste Gratuito
    is_courtesy BOOLEAN DEFAULT FALSE,
    courtesy_expires_at TIMESTAMP NULL,
    courtesy_reason VARCHAR(255) NULL,
    is_trial BOOLEAN DEFAULT FALSE,
    trial_expires_at TIMESTAMP NULL,
    trial_days INTEGER NULL DEFAULT 30,
    
    -- Dados Adicionais
    description TEXT NULL,
    max_payments INTEGER NULL,
    external_reference VARCHAR(255) NULL,
    
    -- Desconto
    discount_value DECIMAL(8,2) NULL,
    discount_type ENUM('FIXED', 'PERCENTAGE') NULL,
    discount_due_date_limit_days INTEGER NULL DEFAULT 0,
    
    -- Multa e Juros
    fine_value DECIMAL(8,2) NULL DEFAULT 0.00,
    interest_value DECIMAL(8,2) NULL DEFAULT 0.00,
    
    -- Controle
    deleted BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_organization_id (organization_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_courtesy_expires_at (courtesy_expires_at),
    INDEX idx_trial_expires_at (trial_expires_at),
    INDEX idx_next_due_date (next_due_date),
    INDEX idx_is_courtesy (is_courtesy),
    INDEX idx_is_trial (is_trial),
    INDEX idx_external_reference (external_reference)
);
```

### **Campos Principais**

#### **Relacionamentos**
- `organization_id`: Relacionamento único com Organization

#### **Controle de Assinatura**
- `status`: Status da assinatura (ACTIVE, INACTIVE, SUSPENDED, etc.)
- `billing_type`: Tipo de cobrança
- `cycle`: Ciclo de cobrança
- `value`: Valor da assinatura

#### **Datas de Controle**
- `started_at`: Data de início da assinatura
- `expires_at`: Data de expiração
- `next_due_date`: Próxima data de vencimento
- `end_date`: Data final da assinatura

#### **Sistema de Cortesia**
- `is_courtesy`: Flag de cortesia
- `courtesy_expires_at`: Data de expiração da cortesia
- `courtesy_reason`: Motivo da cortesia

#### **Sistema de Teste**
- `is_trial`: Flag de teste gratuito
- `trial_expires_at`: Data de expiração do teste
- `trial_days`: Dias de teste

#### **Dados Financeiros**
- `discount_value/type`: Sistema de desconto
- `fine_value`: Valor da multa
- `interest_value`: Valor dos juros

## 🔧 **IMPLEMENTAÇÃO**

### **1. Model**
```php
class Subscription extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $fillable = [
        'organization_id',
        'status',
        'billing_type',
        'cycle',
        'value',
        'started_at',
        'expires_at',
        'next_due_date',
        'end_date',
        'is_courtesy',
        'courtesy_expires_at',
        'courtesy_reason',
        'is_trial',
        'trial_expires_at',
        'trial_days',
        'description',
        'max_payments',
        'external_reference',
        'discount_value',
        'discount_type',
        'discount_due_date_limit_days',
        'fine_value',
        'interest_value',
        'deleted',
    ];
    
    protected $casts = [
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'next_due_date' => 'datetime',
        'end_date' => 'datetime',
        'courtesy_expires_at' => 'datetime',
        'trial_expires_at' => 'datetime',
        'is_courtesy' => 'boolean',
        'is_trial' => 'boolean',
        'deleted' => 'boolean',
        'value' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'fine_value' => 'decimal:2',
        'interest_value' => 'decimal:2',
    ];
    
    // Relacionamentos
    public function organization(): BelongsTo
    public function asaasSubscription(): HasOne
    
    // Métodos de negócio
    public function isActive(): bool
    public function isExpired(): bool
    public function isInCourtesy(): bool
    public function isInTrial(): bool
    public function canAccessSystem(): bool
    public function getDaysUntilExpiration(): int
}
```

### **2. Domain**
```php
class Subscription
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly string $status,
        public readonly string $billing_type,
        public readonly string $cycle,
        public readonly float $value,
        public readonly Carbon $started_at,
        public readonly ?Carbon $expires_at,
        public readonly ?Carbon $next_due_date,
        public readonly ?Carbon $end_date,
        public readonly bool $is_courtesy,
        public readonly ?Carbon $courtesy_expires_at,
        public readonly ?string $courtesy_reason,
        public readonly bool $is_trial,
        public readonly ?Carbon $trial_expires_at,
        public readonly ?int $trial_days,
        // ... outros campos
        public readonly ?Organization $organization = null,
        public readonly ?AsaasSubscription $asaasSubscription = null,
    ) {}
    
    // Métodos de negócio
    public function isActive(): bool
    public function isExpired(): bool
    public function isInCourtesy(): bool
    public function isInTrial(): bool
    public function canAccessSystem(): bool
    public function needsRenewal(): bool
    public function getAccessType(): string // 'courtesy', 'trial', 'paid', 'expired'
    
    // Métodos de transformação
    public function toArray(): array
    public function toStoreArray(): array
    public function toUpdateArray(): array
}
```

### **3. Repository**
```php
class SubscriptionRepository
{
    // Métodos de busca
    public function findById(int $id): ?Subscription
    public function findByOrganizationId(int $organizationId): ?Subscription
    public function findByExternalReference(string $externalReference): ?Subscription
    
    // Métodos de persistência
    public function save(Subscription $subscription): Subscription
    public function create(Subscription $subscription): Subscription
    public function update(Subscription $subscription): Subscription
    public function delete(int $id): bool
    
    // Métodos de listagem
    public function getAll(int $limit = 100, int $offset = 0): Collection
    public function findExpiring(int $days = 7): Collection
    public function findExpired(): Collection
    public function findActive(): Collection
    public function findCourtesies(): Collection
    public function findTrials(): Collection
    
    // Métodos de filtros
    public function findWithFilters(array $filters = [], int $limit = 100): Collection
}
```

## 🚀 **USE CASES**

### **1. StoreSubscription**
```php
class StoreSubscription
{
    public function perform(array $data): Subscription
    {
        // 1. Validar dados de entrada
        // 2. Verificar se organização já tem assinatura
        // 3. Criar subscription
        // 4. Definir datas baseado no tipo (cortesia/trial/paga)
        // 5. Salvar no banco
        // 6. Retornar domain
    }
}
```

### **2. UpdateSubscription**
```php
class UpdateSubscription
{
    public function perform(int $id, array $data): Subscription
    {
        // 1. Buscar subscription existente
        // 2. Validar dados de entrada
        // 3. Atualizar dados
        // 4. Recalcular datas se necessário
        // 5. Salvar alterações
        // 6. Retornar domain atualizado
    }
}
```

### **3. DeleteSubscription**
```php
class DeleteSubscription
{
    public function perform(int $id): bool
    {
        // 1. Buscar subscription
        // 2. Verificar se pode ser deletada
        // 3. Soft delete ou hard delete
        // 4. Log da operação
        // 5. Retornar resultado
    }
}
```

### **4. GetSubscription**
```php
class GetSubscription
{
    public function perform(int $id): Subscription
    {
        // 1. Buscar subscription por ID
        // 2. Carregar relacionamentos necessários
        // 3. Retornar domain
    }
    
    public function performByOrganization(int $organizationId): Subscription
    {
        // 1. Buscar subscription por organização
        // 2. Carregar relacionamentos necessários
        // 3. Retornar domain
    }
}
```

### **5. GetAllSubscriptions**
```php
class GetAllSubscriptions
{
    public function perform(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        // 1. Aplicar filtros
        // 2. Buscar subscriptions
        // 3. Carregar relacionamentos
        // 4. Retornar array paginado
    }
}
```

## 🔗 **RELACIONAMENTOS**

### **Organization Model - Adicionar Relacionamento**
```php
// app/Models/Organization.php
public function subscription(): HasOne
{
    return $this->hasOne(Subscription::class);
}

public function hasActiveSubscription(): bool
{
    return $this->subscription !== null && $this->subscription->canAccessSystem();
}

public function getSubscriptionStatus(): string
{
    return $this->subscription?->status ?? 'NONE';
}
```

### **AsaasSubscription Model - Adicionar Relacionamento**
```php
// app/Services/ASAAS/Models/AsaasSubscription.php
public function subscription(): BelongsTo
{
    return $this->belongsTo(Subscription::class);
}
```

## 📝 **TASKS DE IMPLEMENTAÇÃO**

### **Fase 1: Estrutura Base (Sprint 1)**
- [ ] **Task 1.1**: Criar migration `create_subscriptions_table`
- [ ] **Task 1.2**: Implementar Model `Subscription`
- [ ] **Task 1.3**: Implementar Domain `Subscription`
- [ ] **Task 1.4**: Implementar Factory `SubscriptionFactory`
- [ ] **Task 1.5**: Implementar Repository `SubscriptionRepository`

### **Fase 2: Use Cases (Sprint 1-2)**
- [ ] **Task 2.1**: Implementar `StoreSubscription`
- [ ] **Task 2.2**: Implementar `UpdateSubscription`
- [ ] **Task 2.3**: Implementar `DeleteSubscription`
- [ ] **Task 2.4**: Implementar `GetSubscription`
- [ ] **Task 2.5**: Implementar `GetAllSubscriptions`

### **Fase 3: Relacionamentos e Integração (Sprint 2)**
- [ ] **Task 3.1**: Adicionar relacionamentos em Organization
- [ ] **Task 3.2**: Atualizar AsaasSubscription para relacionar com Subscription
- [ ] **Task 3.3**: Implementar lógica de acesso baseada em subscription

### **Fase 4: Testes (Sprint 2-3)**
- [ ] **Task 4.1**: Testes unitários completos
- [ ] **Task 4.2**: Testes de integração
- [ ] **Task 4.3**: Testes dos Use Cases

## 💡 **EXEMPLOS PRÁTICOS**

### **Exemplo 1: Criar Cortesia**
```php
$storeSubscription = app(StoreSubscription::class);
$subscription = $storeSubscription->perform([
    'organization_id' => 1,
    'is_courtesy' => true,
    'courtesy_expires_at' => now()->addDays(30),
    'courtesy_reason' => 'Cliente especial',
    'value' => 0.00,
]);
```

### **Exemplo 2: Criar Teste Gratuito**
```php
$subscription = $storeSubscription->perform([
    'organization_id' => 2,
    'is_trial' => true,
    'trial_days' => 15,
    'value' => 0.00,
]);
```

### **Exemplo 3: Criar Assinatura Paga**
```php
$subscription = $storeSubscription->perform([
    'organization_id' => 3,
    'status' => 'ACTIVE',
    'billing_type' => 'BOLETO',
    'cycle' => 'MONTHLY',
    'value' => 99.90,
    'started_at' => now(),
    'next_due_date' => now()->addMonth(),
]);
```

---

**Status**: 📋 Planejamento  
**Prioridade**: 🔥 Alta  
**Estimativa**: 2-3 sprints  
**Dependências**: Epic 2 (AsaasOrganizationCustomer) concluído  
**Responsável**: Equipe Backend  
**Reviewer**: Tech Lead
