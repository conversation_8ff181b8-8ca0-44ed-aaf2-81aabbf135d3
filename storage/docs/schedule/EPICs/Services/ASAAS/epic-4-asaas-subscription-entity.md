# 🎯 **EPIC 4: AsaasSubscription Entity**

## 📋 **RESUMO**

Implementação completa da entidade `AsaasSubscription` para integração com a API de assinaturas do ASAAS. Esta entidade será responsável por gerenciar assinaturas dos customers (AsaasOrganizationCustomers) no sistema ASAAS, mantendo sincronização bidirecional e controle de estado.

## 🎯 **OBJETIVOS**

### **Objetivo Principal**
Criar uma entidade completa para gerenciar assinaturas do ASAAS, incluindo sincronização com API, relacionamentos corretos e Use Cases para operações principais.

### **Objetivos Específicos**
1. ✅ Criação completa da entidade AsaasSubscription (migration, model, domain, factory, repository)
2. ✅ Implementação de Use Cases para comunicação com API ASAAS
3. ✅ Relacionamentos corretos com Subscription e AsaasOrganizationCustomer
4. ✅ Sincronização bidirecional com ASAAS
5. ✅ Controle de estado e validações

## 🏗️ **ARQUITETURA**

### **1. Conceito da Entidade**

```
AsaasSubscription:
- Entidade de integração com ASAAS API
- Relacionada com Subscription (subscription_id)
- Relacionada com AsaasOrganizationCustomer (asaas_customer_id)
- Mantém dados específicos do ASAAS
- Sincronização bidirecional

Fluxos:
1. Criar Subscription → Criar AsaasSubscription → Sync com ASAAS
2. Webhook ASAAS → Atualizar AsaasSubscription → Atualizar Subscription
3. Cancelar Subscription → Cancelar AsaasSubscription → Sync com ASAAS
```

### **2. Estrutura de Arquivos**

```
database/migrations/
├── xxxx_create_asaas_subscriptions_table.php

app/Services/ASAAS/Models/
├── AsaasSubscription.php

app/Services/ASAAS/Domains/
├── AsaasSubscription.php

app/Services/ASAAS/Factories/
├── AsaasSubscriptionFactory.php

app/Services/ASAAS/Repositories/
├── AsaasSubscriptionRepository.php

app/Services/ASAAS/UseCases/Subscriptions/
├── CreateAsaasSubscription.php
├── UpdateAsaasSubscription.php
├── CancelAsaasSubscription.php
├── SyncAsaasSubscription.php
├── GetAsaasSubscription.php
```

## 📊 **ESTRUTURA DE DADOS**

### **Migration: asaas_subscriptions**

```sql
CREATE TABLE asaas_subscriptions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Relacionamentos
    subscription_id BIGINT UNSIGNED NOT NULL UNIQUE,
    asaas_customer_id VARCHAR(255) NOT NULL,
    
    -- Dados do ASAAS
    asaas_subscription_id VARCHAR(255) NOT NULL UNIQUE,
    asaas_date_created DATE NOT NULL,
    asaas_synced_at TIMESTAMP NULL,
    asaas_sync_errors JSON NULL,
    sync_status ENUM('pending', 'synced', 'error') DEFAULT 'pending',
    
    -- Dados da Assinatura ASAAS
    billing_type ENUM('BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED') NOT NULL,
    cycle ENUM('MONTHLY', 'QUARTERLY', 'SEMIANNUAL', 'YEARLY') NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    next_due_date DATE NULL,
    end_date DATE NULL,
    description TEXT NULL,
    status ENUM('ACTIVE', 'INACTIVE', 'EXPIRED', 'CANCELLED') NOT NULL,
    max_payments INTEGER NULL,
    external_reference VARCHAR(255) NULL,
    payment_link VARCHAR(500) NULL,
    checkout_session VARCHAR(255) NULL,
    
    -- Desconto
    discount_value DECIMAL(8,2) NULL,
    discount_type ENUM('FIXED', 'PERCENTAGE') NULL,
    discount_due_date_limit_days INTEGER NULL DEFAULT 0,
    
    -- Multa e Juros
    fine_value DECIMAL(8,2) NULL,
    fine_type ENUM('FIXED', 'PERCENTAGE') NULL DEFAULT 'FIXED',
    interest_value DECIMAL(8,2) NULL,
    
    -- Cartão de Crédito
    credit_card_number VARCHAR(20) NULL,
    credit_card_brand VARCHAR(50) NULL,
    credit_card_token VARCHAR(255) NULL,
    
    -- Split (JSON para múltiplos splits)
    split_data JSON NULL,
    
    -- Controle
    deleted BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_asaas_customer_id (asaas_customer_id),
    INDEX idx_asaas_subscription_id (asaas_subscription_id),
    INDEX idx_status (status),
    INDEX idx_sync_status (sync_status),
    INDEX idx_next_due_date (next_due_date),
    INDEX idx_external_reference (external_reference),
    INDEX idx_asaas_synced_at (asaas_synced_at)
);
```

### **Campos Principais**

#### **Relacionamentos**
- `subscription_id`: Relacionamento único com Subscription
- `asaas_customer_id`: Referência ao customer no ASAAS

#### **Identificação ASAAS**
- `asaas_subscription_id`: ID único da assinatura no ASAAS
- `asaas_date_created`: Data de criação no ASAAS

#### **Controle de Sincronização**
- `asaas_synced_at`: Última sincronização
- `asaas_sync_errors`: Erros de sincronização
- `sync_status`: Status da sincronização

#### **Dados da Assinatura**
- `billing_type`, `cycle`, `value`: Dados básicos
- `next_due_date`, `end_date`: Datas importantes
- `status`: Status no ASAAS

#### **Dados Financeiros**
- `discount_*`: Sistema de desconto
- `fine_*`: Sistema de multa
- `interest_value`: Juros

#### **Dados de Pagamento**
- `credit_card_*`: Informações do cartão
- `payment_link`: Link de pagamento
- `split_data`: Dados de split (JSON)

## 🔧 **IMPLEMENTAÇÃO**

### **1. Model**
```php
class AsaasSubscription extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $fillable = [
        'subscription_id',
        'asaas_customer_id',
        'asaas_subscription_id',
        'asaas_date_created',
        'asaas_synced_at',
        'asaas_sync_errors',
        'sync_status',
        'billing_type',
        'cycle',
        'value',
        'next_due_date',
        'end_date',
        'description',
        'status',
        'max_payments',
        'external_reference',
        'payment_link',
        'checkout_session',
        'discount_value',
        'discount_type',
        'discount_due_date_limit_days',
        'fine_value',
        'fine_type',
        'interest_value',
        'credit_card_number',
        'credit_card_brand',
        'credit_card_token',
        'split_data',
        'deleted',
    ];
    
    protected $casts = [
        'asaas_date_created' => 'date',
        'asaas_synced_at' => 'datetime',
        'next_due_date' => 'date',
        'end_date' => 'date',
        'asaas_sync_errors' => 'array',
        'split_data' => 'array',
        'deleted' => 'boolean',
        'value' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'fine_value' => 'decimal:2',
        'interest_value' => 'decimal:2',
    ];
    
    // Relacionamentos
    public function subscription(): BelongsTo
    public function asaasCustomer(): BelongsTo
    
    // Métodos de negócio
    public function isActive(): bool
    public function needsSync(): bool
    public function hasErrors(): bool
    public function toAsaasPayload(): array
}
```

### **2. Domain**
```php
class AsaasSubscription
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $subscription_id,
        public readonly string $asaas_customer_id,
        public readonly string $asaas_subscription_id,
        public readonly Carbon $asaas_date_created,
        public readonly ?Carbon $asaas_synced_at,
        public readonly ?array $asaas_sync_errors,
        public readonly string $sync_status,
        public readonly string $billing_type,
        public readonly string $cycle,
        public readonly float $value,
        public readonly ?Carbon $next_due_date,
        public readonly ?Carbon $end_date,
        public readonly ?string $description,
        public readonly string $status,
        // ... outros campos
        public readonly ?Subscription $subscription = null,
        public readonly ?AsaasOrganizationCustomer $asaasCustomer = null,
    ) {}
    
    // Métodos de negócio
    public function isActive(): bool
    public function needsSync(): bool
    public function hasErrors(): bool
    public function toAsaasPayload(): array
    public function toAsaasUpdatePayload(): array
}
```

## 🚀 **USE CASES**

### **1. CreateAsaasSubscription**
```php
class CreateAsaasSubscription
{
    public function perform(Subscription $subscription): AsaasSubscription
    {
        // 1. Validar se subscription pode ter AsaasSubscription
        // 2. Buscar AsaasOrganizationCustomer
        // 3. Preparar payload para ASAAS
        // 4. Criar assinatura no ASAAS via API
        // 5. Salvar AsaasSubscription no banco
        // 6. Retornar domain
    }
}
```

### **2. UpdateAsaasSubscription**
```php
class UpdateAsaasSubscription
{
    public function perform(AsaasSubscription $asaasSubscription, array $data): AsaasSubscription
    {
        // 1. Validar dados de entrada
        // 2. Preparar payload de atualização
        // 3. Atualizar no ASAAS via API
        // 4. Atualizar no banco local
        // 5. Retornar domain atualizado
    }
}
```

### **3. CancelAsaasSubscription**
```php
class CancelAsaasSubscription
{
    public function perform(AsaasSubscription $asaasSubscription): AsaasSubscription
    {
        // 1. Cancelar no ASAAS via API
        // 2. Atualizar status local
        // 3. Atualizar Subscription relacionada
        // 4. Retornar domain atualizado
    }
}
```

### **4. SyncAsaasSubscription**
```php
class SyncAsaasSubscription
{
    public function perform(AsaasSubscription $asaasSubscription): AsaasSubscription
    {
        // 1. Buscar dados atuais no ASAAS
        // 2. Comparar com dados locais
        // 3. Atualizar dados locais se necessário
        // 4. Atualizar Subscription se necessário
        // 5. Marcar como sincronizado
    }
}
```

### **5. GetAsaasSubscription**
```php
class GetAsaasSubscription
{
    public function perform(int $id): AsaasSubscription
    public function performBySubscriptionId(int $subscriptionId): AsaasSubscription
    public function performByAsaasId(string $asaasSubscriptionId): AsaasSubscription
}
```

## 🔗 **RELACIONAMENTOS**

### **Subscription Model - Adicionar Relacionamento**
```php
// app/Models/Subscription.php
public function asaasSubscription(): HasOne
{
    return $this->hasOne(AsaasSubscription::class);
}

public function hasAsaasIntegration(): bool
{
    return $this->asaasSubscription !== null;
}
```

### **AsaasOrganizationCustomer Model - Adicionar Relacionamento**
```php
// app/Services/ASAAS/Models/AsaasOrganizationCustomer.php
public function asaasSubscriptions(): HasMany
{
    return $this->hasMany(AsaasSubscription::class, 'asaas_customer_id', 'asaas_customer_id');
}
```

## 📝 **TASKS DE IMPLEMENTAÇÃO**

### **Fase 1: Estrutura Base (Sprint 1)**
- [ ] **Task 1.1**: Criar migration `create_asaas_subscriptions_table`
- [ ] **Task 1.2**: Implementar Model `AsaasSubscription`
- [ ] **Task 1.3**: Implementar Domain `AsaasSubscription`
- [ ] **Task 1.4**: Implementar Factory `AsaasSubscriptionFactory`
- [ ] **Task 1.5**: Implementar Repository `AsaasSubscriptionRepository`

### **Fase 2: Use Cases (Sprint 1-2)**
- [ ] **Task 2.1**: Implementar `CreateAsaasSubscription`
- [ ] **Task 2.2**: Implementar `UpdateAsaasSubscription`
- [ ] **Task 2.3**: Implementar `CancelAsaasSubscription`
- [ ] **Task 2.4**: Implementar `SyncAsaasSubscription`
- [ ] **Task 2.5**: Implementar `GetAsaasSubscription`

### **Fase 3: Relacionamentos e Integração (Sprint 2)**
- [ ] **Task 3.1**: Adicionar relacionamentos em Subscription
- [ ] **Task 3.2**: Adicionar relacionamentos em AsaasOrganizationCustomer
- [ ] **Task 3.3**: Implementar sincronização automática

### **Fase 4: Testes (Sprint 2-3)**
- [ ] **Task 4.1**: Testes unitários completos
- [ ] **Task 4.2**: Testes de integração com API
- [ ] **Task 4.3**: Testes dos Use Cases

## 💡 **EXEMPLOS PRÁTICOS**

### **Exemplo 1: Criar Assinatura ASAAS**
```php
$createUseCase = app(CreateAsaasSubscription::class);
$asaasSubscription = $createUseCase->perform($subscription);
```

### **Exemplo 2: Sincronizar com ASAAS**
```php
$syncUseCase = app(SyncAsaasSubscription::class);
$asaasSubscription = $syncUseCase->perform($asaasSubscription);
```

### **Exemplo 3: Cancelar Assinatura**
```php
$cancelUseCase = app(CancelAsaasSubscription::class);
$asaasSubscription = $cancelUseCase->perform($asaasSubscription);
```

---

**Status**: 📋 Planejamento  
**Prioridade**: 🔥 Alta  
**Estimativa**: 2-3 sprints  
**Dependências**: Epic 3 (Subscriptions) concluído  
**Responsável**: Equipe Backend  
**Reviewer**: Tech Lead
