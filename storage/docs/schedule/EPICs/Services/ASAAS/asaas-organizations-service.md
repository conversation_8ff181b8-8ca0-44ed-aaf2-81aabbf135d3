# Epic: Integração com ASAAS - Organizações e Subcontas

## 📋 Overview

Para que as Organizações (clientes do nosso sistema) possam operar plenamente com recursos financeiros e de cobrança via ASAAS, é necessária uma integração dedicada. Esta Epic trata da criação e gestão de subcontas ASAAS para cada Organização, bem como da gestão da assinatura do uso do sistema (com ou sem ASAAS), para controle de acesso ao sistema principal.

**Pré-requisito:** Esta Epic assume que a [Epic: Camada de Serviço ASAAS Base](./asaas-main-service.md) já foi implementada.

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ❌ | Integração de subconta ASAAS para Organizações |
| ❌ | Criação de assinatura ASAAS para organização |
| ❌ | Lógica de cortesia para organização usar sistema sem ASAAS |
| ⚠️ | Validação de acesso ao sistema baseada em pagamento (precária) |
| ❌ | Consulta de faturas/assinaturas das organizações via painel |
| ✅ | Modelo Organization com `is_active` e `is_suspended` |
| ✅ | Validação de organização em UseCases existentes |

## 🚀 Melhorias Propostas

### 1. Cadastro de subconta ASAAS para cada organização
Na criação de uma nova Organização (ou via trigger), criar automaticamente uma subconta ASAAS vinculada a ela via endpoint `/accounts` da ASAAS. Armazenar `asaas_account_id` e `asaas_api_key` da subconta para uso em chamadas futuras.

### 2. Assinatura ASAAS principal para uso do sistema
Criar automaticamente uma assinatura ASAAS para uso do sistema ao ativar a Organização, com produto de assinatura criado previamente no ASAAS (ex: "Licença Mensal"). Caso seja concedida uma cortesia, pular esta etapa.

### 3. Controle de cortesia (sem integração ASAAS)
Permitir marcar uma Organização como "cortesia ativa" via campo `is_courtesy`. Com isso, o sistema considera que ela está em dia mesmo sem assinatura ASAAS, apenas para fins internos.

### 4. Lógica de acesso baseada em status ASAAS ou cortesia
Integrar com middleware existente para validar se a Organização tem assinatura ativa ASAAS ou cortesia vigente, respeitando os campos `is_active` e `is_suspended` já existentes.

### 5. Visualização de cobranças e assinatura
Permitir que o admin da Organização visualize no painel suas cobranças ASAAS, próximas faturas, status e histórico de pagamento via nova rota `/organizations/billing`.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Criação de subconta ASAAS na criação da organização
- **Fase 2:** Criação de assinatura ASAAS e controle de cortesia
- **Fase 3:** Validação de acesso ao sistema com base na situação ASAAS/cortesia
- **Fase 4:** Visualização de cobranças e status da organização
- **Fase 5:** Testes, proteção de fluxo e melhorias UX

## 🔧 Plano de Implementação Detalhado

### 1. Criação de subconta ASAAS

**Migration:**
- Adicionar campos `asaas_account_id`, `asaas_api_key`, `is_courtesy` à tabela `organizations`

**UseCases:**
- `app/Services/ASAAS/UseCases/Organizations/CreateSubaccount.php` - Consome endpoint `/accounts` da ASAAS usando `AsaasService`
- Modificar `app/UseCases/Organization/Store.php` para disparar criação de subconta

**Validações:**
- Validar se a subconta já existe antes de criar
- Rollback da organização se falhar criação da subconta

### 2. Assinatura ASAAS + Controle de cortesia

**Campos adicionais:**
- `organizations.asaas_subscription_id` (string, nullable)
- `organizations.subscription_expires_at` (datetime, nullable)
- `organizations.is_courtesy` (boolean, default false)

**UseCases:**
- `app/Services/ASAAS/UseCases/Organizations/CreateSystemSubscription.php`
- `app/Services/ASAAS/UseCases/Organizations/CheckSubscriptionStatus.php`

**Domains:**
- `app/Services/ASAAS/Domains/OrganizationSubscription.php` - Encapsula lógica de assinatura

### 3. Validação de acesso

**Middleware:**
- `app/Http/Middleware/CheckOrganizationAccess.php` - Valida acesso baseado em assinatura/cortesia
- Integrar com middleware de autenticação existente

**UseCases:**
- `app/Services/ASAAS/UseCases/Organizations/IsAllowedToUseSystem.php`
- Respeitar campos existentes `is_active` e `is_suspended`

### 4. Visualização no painel

**Rotas:**
- `GET /api/organizations/billing` - Mostra dados de cobrança da organização logada
- Integrar com `OrganizationController` existente

**UseCases:**
- `app/Services/ASAAS/UseCases/Organizations/GetBillingDetails.php`

### 5. Testes e UX

**Testes:**
- Unitários para todos os UseCases seguindo padrão `tests/Feature/Services/ASAAS/Organizations/`
- Integração com endpoint real ASAAS sandbox
- Testes de middleware de acesso

**Proteções:**
- Transações de banco para rollback em falhas
- Logs via `DBLog` para auditoria

## 📦 Previsão de Arquivos do PR

### Migrations
```
database/migrations/xxxx_add_asaas_fields_to_organizations_table.php (novo)
```

### Models (modificados)
```
app/Models/Organization.php (modificado - adicionar campos ASAAS)
app/Domains/Organization.php (modificado - adicionar campos ASAAS)
app/Factories/OrganizationFactory.php (modificado - suporte aos novos campos)
```

### Services
```
app/Services/ASAAS/Domains/OrganizationSubscription.php (novo)
```

### UseCases
```
app/Services/ASAAS/UseCases/Organizations/CreateSubaccount.php (novo)
app/Services/ASAAS/UseCases/Organizations/CreateSystemSubscription.php (novo)
app/Services/ASAAS/UseCases/Organizations/GetBillingDetails.php (novo)
app/Services/ASAAS/UseCases/Organizations/IsAllowedToUseSystem.php (novo)
app/Services/ASAAS/UseCases/Organizations/CheckSubscriptionStatus.php (novo)
app/UseCases/Organization/Store.php (modificado - integrar criação de subconta)
```

### Middleware
```
app/Http/Middleware/CheckOrganizationAccess.php (novo)
```

### Controllers (modificados)
```
app/Http/Controllers/OrganizationController.php (modificado - adicionar rota billing)
```

### Routes (modificados)
```
routes/api.php (modificado - adicionar rota /organizations/billing)
```

### Enums
```
app/Enums/SubscriptionStatus.php (novo)
```

### Tests
```
tests/Feature/Services/ASAAS/Organizations/CreateSubaccountTest.php (novo)
tests/Feature/Services/ASAAS/Organizations/SubscriptionTest.php (novo)
tests/Feature/Services/ASAAS/Organizations/AccessControlTest.php (novo)
tests/Feature/Services/ASAAS/Organizations/BillingTest.php (novo)
```

**Total Estimado:** ~18 arquivos (13 novos + 5 modificados)

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Organizações não possuem subconta ASAAS

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Organizações criadas sem integração com ASAAS |
| **Impacto** | Não é possível gerenciar cobranças e pagamentos por organização |
| **Solução** | Criar subconta no ASAAS automaticamente ao registrar a organização usando `AsaasService` |

### Problema 2: Controle de acesso é inconsistente

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Apenas campos `is_active` e `is_suspended` para controle |
| **Impacto** | Organizações sem pagamento continuam acessando o sistema |
| **Solução** | Middleware integrado que valida assinatura ASAAS + cortesia + campos existentes |

### Problema 3: Não é possível conceder cortesia com rastreabilidade

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Cortesias dadas "no boca a boca", sem consistência |
| **Impacto** | Falta de controle sobre organizações em período de cortesia |
| **Solução** | Campo `is_courtesy` com tratativa clara no fluxo de validação |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Criação de subconta ASAAS
- ✅ Criação de assinatura ASAAS
- ✅ Middleware de acesso com diferentes cenários
- ✅ Validação de cortesia vs assinatura ativa
- ✅ Integração com campos `is_active` e `is_suspended` existentes

### Testes de Integração
- ✅ Fluxo completo: criação organização → subconta → assinatura
- ✅ Cenários com e sem cortesia
- ✅ Teste de rollback em falhas de criação
- ✅ Integração com `AsaasService` em ambiente sandbox

### Testes de Regressão
- ✅ Cadastro de organização continua funcionando mesmo sem ASAAS ativo
- ✅ Login e acesso validados corretamente
- ✅ UseCases existentes de Organization continuam funcionando
- ✅ Compatibilidade com estrutura atual de middleware

## 🎯 Conclusão

Com esta Epic, garantimos que cada Organização está estruturada para operar de forma independente com relação a cobranças, status de uso do sistema e controle financeiro. A integração com ASAAS e a opção de cortesia tornam o sistema flexível para freemium, testes e modelos especiais de contrato, mantendo compatibilidade total com a estrutura existente.

## 📈 Benefícios Esperados

### Técnicos
- ✅ Integração de subcontas centralizada via `AsaasService`
- ✅ Status de organização unificado via ASAAS/cortesia
- ✅ Logs padronizados via `DBLog` para auditoria
- ✅ Middleware reutilizável para controle de acesso
- ✅ Compatibilidade com estrutura existente de Organization

### De Negócio
- 💰 Facilita cobrança automatizada por organização
- 🔒 Oferece freemium de forma segura e controlada
- 📊 Aumenta controle de inadimplência
- ⚡ Reduz tempo de setup para novas organizações

### De Usuário
- 🔍 Transparência nas cobranças e status
- 🚫 Menos erros operacionais e bloqueios inesperados
- 📱 Interface unificada para gestão de assinatura

## 💼 Impacto no Negócio

- 🏢 Permite escalar para dezenas de organizações com billing separado
- 📈 Reduz inadimplência e melhora previsibilidade financeira
- 🎯 Abre possibilidade de planos personalizados
- 🔄 Base sólida para expansão do modelo de negócio SaaS

## 📚 Referências

- [ASAAS API - Subcontas](https://docs.asaas.com/reference/subcontas)
- [ASAAS API - Assinaturas](https://docs.asaas.com/reference/criar-assinatura)
- [Epic: Camada de Serviço ASAAS Base](./asaas-main-service.md)
- [Laravel Middleware](https://laravel.com/docs/middleware)
- [Epic: Integração com Vendas e Clientes ASAAS](./asaas-sales-service.md) *(próxima Epic)*

---

**Próximos Passos:** Após a implementação desta Epic, será possível desenvolver a integração específica para Vendas/Clientes, utilizando as subcontas ASAAS já configuradas para cada organização.
