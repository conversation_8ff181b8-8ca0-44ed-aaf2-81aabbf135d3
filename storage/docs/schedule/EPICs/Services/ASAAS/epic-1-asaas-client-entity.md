# Epic 1 - Criar Entidade AsaasClient (Customer no Asaas)

## 📋 **RESUMO**

Criação da entidade completa AsaasClient que representa um customer do Asaas no nosso sistema. Esta entidade será linkada a um Client existente e permitirá sincronização bidirecional com a API do Asaas.

## 🎯 **OBJETIVOS**

- Criar migration para tabela `asaas_clients`
- Implementar Model `AsaasClient` com relacionamentos
- Desenvolver Factory para criação de instâncias
- Criar Domain `AsaasClient` com regras de negócio
- Implementar Repository para acesso a dados
- Desenvolver Use Cases principais (Create, Update, Sync, Delete)
- Domains terá todos os atributos das colunas dos bancos.

## 📊 **ESTRUTURA DA ENTIDADE**

### **Campos da Tabela `asaas_clients`**

```sql
CREATE TABLE asaas_clients (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    client_id BIGINT NOT NULL,
    organization_id BIGINT NOT NULL,
    asaas_customer_id VARCHAR(255) UNIQUE NOT NULL,
    
    -- Sync Control
    asaas_synced_at TIMESTAMP NULL,
    asaas_sync_errors JSON NULL,
    sync_status ENUM('pending', 'synced', 'error') DEFAULT 'pending',
    
    -- Customer Data from Asaas API
    name VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    mobile_phone VARCHAR(20) NULL,
    address VARCHAR(255) NULL,
    address_number VARCHAR(10) NULL,
    complement VARCHAR(255) NULL,
    province VARCHAR(100) NULL,
    city_name VARCHAR(100) NULL,
    state VARCHAR(2) NULL,
    country VARCHAR(100) NULL,
    postal_code VARCHAR(10) NULL,
    cpf_cnpj VARCHAR(18) NULL,
    person_type ENUM('FISICA', 'JURIDICA') NULL,
    external_reference VARCHAR(255) NULL,
    notification_disabled BOOLEAN DEFAULT FALSE,
    additional_emails TEXT NULL,
    observations TEXT NULL,
    foreign_customer BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    
    INDEX idx_asaas_customer_id (asaas_customer_id),
    INDEX idx_client_id (client_id),
    INDEX idx_organization_id (organization_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_asaas_synced_at (asaas_synced_at)
);
```

## 🏗️ **ARQUITETURA**

### **1. Migration**
```
database/migrations/xxxx_create_asaas_clients_table.php
```

### **2. Model**
```
app/Services/ASAAS/Models/AsaasClient.php
```
- Relacionamentos: `belongsTo(Client::class)`, `belongsTo(Organization::class)`
- Casts para JSON e boolean
- Scopes para filtros comuns

### **3. Domain**
```
app/Services/ASAAS/Domains/AsaasClient.php
```
- Validações de negócio
- Métodos de transformação para API
- Controle de sincronização

### **4. Factory**
```
app/Services/ASAAS/Factories/AsaasClientFactory.php
```
- `buildFromModel()`
- `buildFromAsaasResponse()`
- `buildFromClient()`

### **5. Repository**
```
app/Services/ASAAS/Repositories/AsaasClientRepository.php
```
- CRUD operations
- Métodos de busca específicos
- Controle de sincronização

### **6. Use Cases**
```
app/Services/ASAAS/UseCases/Clients/
├── CreateAsaasClient.php
├── UpdateAsaasClient.php
├── SyncAsaasClient.php
├── DeleteAsaasClient.php
└── GetAsaasClientByCustomerId.php
```

## 📝 **EXEMPLO DE PAYLOAD ASAAS**

### **Request Body (Criação)**
```json
{
    "name": "John Doe",
    "cpfCnpj": "24971563792",
    "email": "<EMAIL>",
    "phone": "4738010919",
    "mobilePhone": "4799376637",
    "address": "Av. Paulista",
    "addressNumber": "150",
    "complement": "Sala 201",
    "province": "Centro",
    "postalCode": "01310-000",
    "externalReference": "12987382",
    "notificationDisabled": false,
    "additionalEmails": "<EMAIL>,<EMAIL>",
    "observations": "ótimo pagador, nenhum problema até o momento",
    "foreignCustomer": false
}
```

### **Response (Asaas API)**
```json
{
    "object": "customer",
    "id": "cus_000005401844",
    "dateCreated": "2024-07-12",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "90999999999",
    "mobilePhone": "90999999999",
    "address": "Av. Paulista",
    "addressNumber": "150",
    "complement": "Sala 201",
    "province": "Centro",
    "city": 12565,
    "cityName": "São Paulo",
    "state": "SP",
    "country": "Brasil",
    "postalCode": "01310000",
    "cpfCnpj": "24971563792",
    "personType": "FISICA",
    "deleted": false,
    "additionalEmails": "<EMAIL>,<EMAIL>",
    "externalReference": "12987382",
    "notificationDisabled": false,
    "observations": "ótimo pagador, nenhum problema até o momento",
    "foreignCustomer": false
}
```

## 🔄 **USE CASES PRINCIPAIS**

### **1. CreateAsaasClient**
- Valida se Client não possui AsaasClient
- Cria customer na API Asaas
- Persiste AsaasClient no banco
- Atualiza Client com asaas_customer_id

### **2. UpdateAsaasClient**
- Sincroniza dados do Client com Asaas
- Atualiza customer na API
- Atualiza registro local

### **3. SyncAsaasClient**
- Busca dados atualizados do Asaas
- Compara com dados locais
- Atualiza diferenças encontradas

### **4. DeleteAsaasClient**
- Marca como deleted no Asaas
- Soft delete local
- Remove referência do Client

## 🧪 **TESTES**

### **Estrutura de Testes**
```
tests/Feature/Services/ASAAS/Clients/
├── CreateAsaasClientTest.php
├── UpdateAsaasClientTest.php
├── SyncAsaasClientTest.php
└── DeleteAsaasClientTest.php

tests/Unit/Services/ASAAS/Domains/AsaasClientTest.php
tests/Unit/Services/ASAAS/Repositories/AsaasClientRepositoryTest.php
```

### **Cenários de Teste**
- Criação com dados válidos
- Validação de duplicidade
- Sincronização bidirecional
- Tratamento de erros da API
- Soft delete e recuperação

## 📦 **ARQUIVOS DO PR**

### **Novos Arquivos**
```
database/migrations/xxxx_create_asaas_clients_table.php
app/Services/ASAAS/Models/AsaasClient.php
app/Services/ASAAS/Domains/AsaasClient.php
app/Services/ASAAS/Factories/AsaasClientFactory.php
app/Services/ASAAS/Repositories/AsaasClientRepository.php
app/Services/ASAAS/UseCases/Clients/CreateAsaasClient.php
app/Services/ASAAS/UseCases/Clients/UpdateAsaasClient.php
app/Services/ASAAS/UseCases/Clients/SyncAsaasClient.php
app/Services/ASAAS/UseCases/Clients/DeleteAsaasClient.php
app/Services/ASAAS/UseCases/Clients/GetAsaasClientByCustomerId.php
database/factories/AsaasClientFactory.php
```

### **Arquivos Modificados**
```
app/Models/Client.php (adicionar relacionamento hasOne AsaasClient)
app/Domains/Inventory/Client.php (adicionar métodos Asaas)
```

## ⚡ **INTEGRAÇÃO COM SISTEMA EXISTENTE**

### **Relacionamentos**
- `AsaasClient belongsTo Client`
- `AsaasClient belongsTo Organization`
- `Client hasOne AsaasClient`

### **Hooks de Sincronização**
- Trigger automático na criação de Client
- Sincronização em background via Jobs
- Webhook handling para updates do Asaas

## 🔒 **VALIDAÇÕES E REGRAS**

### **Validações de Negócio**
- Client deve pertencer à Organization
- CPF/CNPJ deve ser válido
- Email deve ser único por Organization
- Não permitir duplicação de asaas_customer_id

### **Controle de Sincronização**
- Status de sync (pending, synced, error)
- Timestamp da última sincronização
- Log de erros em JSON
- Retry automático para falhas

## 📈 **MÉTRICAS E MONITORAMENTO**

### **Logs de Auditoria**
- Criação de customers
- Falhas de sincronização
- Updates bidirecionais
- Performance de API calls

### **Alertas**
- Taxa de erro > 5%
- Tempo de resposta > 5s
- Falhas de sincronização consecutivas
