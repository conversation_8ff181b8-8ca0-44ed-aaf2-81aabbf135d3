# Plano de Reorganização Completa - Serviço ASAAS

## 🚨 Análise Crítica dos Problemas Identificados

### **Situação Atual: CRÍTICA**
O serviço ASAAS está em estado **INUTILIZÁVEL** devido a problemas estruturais graves que impedem até mesmo a instanciação básica de domains. A arquitetura atual apresenta inconsistências fundamentais que tornam o desenvolvimento mais prejudicial que benéfico.

---
## Padrão de Arquitetura Obrigatório

- **NÃO** existe Model sem Domain , Factory e Repository
- Model **NUNCA** pode ser tocada por nada que não seja repository ou factory, Em qualquer outro lugar é o Domain que deve ser acessado.
- Repositories Recebem e Retornam Dominios SEMPRE. Recebem dominios , consultam o banco com as models , intanciam novos dominios com as factories e retornam os dominios

## Entidades que existem e devem existir no sistema:

Organization - Organização em nosso sistema, AsaasOrganization sempre terá um organization_id referenciando essa entidade
Client - Clientes de uma Organização , AsaasClient sempre terá um client_id referenciando essa entidade
Sale - Vendas registradas em um organização , AsaasSale sempre terá um sale_id referenciando essa entidade
Subscription - Assinatura que permite usuários de uma organização acessar módulos do nosso sistema, pode ser uma cortesia ou ser uma assinatura do Asaas, AsaasSubscription sempre terá um subscription_id associado a Subscription

AsaasOrganization - SubConta de nossa organização no Asaas, pertence a Organization
AsaasClient - Cliente ou Customer cadastrado no Asaas , está atrelado a entidade de Client do nosso sistema
AsaasSale - Venda ou Cobrança no Asaas , está atrelado Sale do nosso sistema
AsaasSubscription - Assinatura no Asaas , está atrelado a Subscription do nosso sistema

## 📋 **PROBLEMAS CRÍTICOS IDENTIFICADOS**

### **1. Confusão Arquitetural Grave**

#### **Problema: Mistura de Responsabilidades**
- **AsaasOrganization Domain** está fazendo o trabalho que deveria ser do **AsaasSubscription**
- **AsaasSubscription Model** existe mas não é usado para nada
- Campos de assinatura estão duplicados entre `organizations` e `asaas_organizations`
- Lógica de negócio espalhada entre Domain, Model e Repository

#### **Evidências:**
```php
// ❌ AsaasOrganization Domain tem campos de subscription
public ?SubscriptionStatus $subscription_status;
public ?float $subscription_value;
public ?Carbon $subscription_expires_at;

// ❌ AsaasSubscription Model existe mas não é usado
class AsaasSubscription extends Model {
    // Campos completos de subscription, mas ninguém usa
}

// ❌ Migration organizations tem campos ASAAS misturados
$table->string('subscription_status')->default('inactive');
$table->decimal('subscription_value', 10, 2)->nullable();
```

### **2. Problemas de Instanciação de Domains**

#### **Problema: Construtores Impossíveis**
```php
// ❌ Organization Domain - construtor com 12 parâmetros obrigatórios
public function __construct(
    ?int $id,
    string $name,                    // obrigatório
    ?string $description,
    ?bool $is_active,
    ?bool $is_suspended,
    ?int $default_flow_id = null,
    ?Carbon $created_at = null,
    ?Carbon $updated_at = null,
    ?bool $is_courtesy = null,
    ?Carbon $courtesy_expires_at = null,
    ?string $courtesy_reason = null,
    ?AsaasOrganization $asaas = null,  // ❌ Dependência circular
)

// ❌ AsaasOrganization Domain - construtor com 20+ parâmetros
// Impossível de instanciar manualmente
```

### **3. Factories Quebradas**

#### **Problema: Factories não conseguem construir domains**
```php
// ❌ OrganizationFactory::buildFromModel() 
// Não consegue instanciar AsaasOrganization porque precisa de OrganizationDomain
// Mas OrganizationDomain precisa de AsaasOrganization
// = DEPENDÊNCIA CIRCULAR IMPOSSÍVEL
```

### **4. Type Hints Inconsistentes**

#### **Problema: Métodos recebem um tipo mas esperam outro**
```php
// ❌ Use Case recebe Organization mas type hint é OrganizationDomain
public function perform(Organization $organization): array  // ❌ Model
// Mas internamente usa como Domain

// ❌ Repository recebe Domain mas salva como Model
public function store(AsaasOrganization $domain): AsaasOrganization
// Mas AsaasOrganization domain não tem toStoreArray() correto
```

### **5. Imports e Namespaces Incorretos**

#### **Problema: Imports que não existem ou estão errados**
```php
// ❌ Imports incorretos encontrados:
use App\Domains\Organization\Organization as OrganizationDomain;  // ❌ Não existe
use App\Domains\ASAAS\Client;  // ❌ Não existe
use App\Services\ASAAS\Models\AsaasOrganization;  // ❌ Confunde Model com Domain
```

### **6. Responsabilidades Mal Definidas**

#### **Problema: Não está claro quem faz o quê**
- **AsaasOrganization Domain**: Mistura dados de conta + subscription + cortesia
- **AsaasSubscription Model**: Existe mas não é usado
- **Organization Domain**: Tem campos ASAAS misturados
- **AsaasOrganization Model**: Duplica campos que deveriam estar em AsaasSubscription

---

## 🎯 **PLANO DE REORGANIZAÇÃO COMPLETA**

### **FASE 1: Separação de Responsabilidades**

#### **1.1 Definir Responsabilidades Claras**

**AsaasAccount (novo):**
- Dados da conta ASAAS (account_id, api_key, environment)
- Métodos de autenticação e conexão

**AsaasSubscription (refatorar):**
- Dados de assinatura (status, valor, datas)
- Lógica de cortesia
- Métodos de verificação de acesso

**AsaasOrganization (simplificar):**
- Apenas agregação de AsaasAccount + AsaasSubscription
- Métodos de conveniência

#### **1.2 Nova Estrutura de Domains**

```php
// ✅ AsaasAccount Domain
class AsaasAccount {
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly string $asaas_account_id,
        public readonly string $asaas_api_key,
        public readonly AsaasEnvironment $environment,
        public readonly bool $is_active = true,
    ) {}
}

// ✅ AsaasSubscription Domain  
class AsaasSubscription {
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly SubscriptionStatus $status,
        public readonly float $value,
        public readonly Carbon $expires_at,
        public readonly bool $is_courtesy = false,
        public readonly ?Carbon $courtesy_expires_at = null,
    ) {}
    
    public function canAccessSystem(): bool { /* lógica aqui */ }
    public function isInCourtesy(): bool { /* lógica aqui */ }
}

// ✅ AsaasOrganization Domain (simplificado)
class AsaasOrganization {
    public function __construct(
        public readonly OrganizationDomain $organization,
        public readonly ?AsaasAccount $account = null,
        public readonly ?AsaasSubscription $subscription = null,
    ) {}
    
    public function canAccessSystem(): bool {
        return $this->subscription?->canAccessSystem() ?? false;
    }
}
```

### **FASE 2: Reorganização de Models e Migrations**

#### **2.1 Separar Tabelas Corretamente**

**Tabela `asaas_accounts`:**
```sql
- id
- organization_id (FK)
- asaas_account_id
- asaas_api_key (encrypted)
- asaas_environment (enum)
- is_active
- created_at, updated_at
```

**Tabela `asaas_subscriptions`:**
```sql
- id  
- organization_id (FK)
- asaas_account_id (FK)
- status (enum)
- value
- started_at, expires_at
- is_courtesy, courtesy_expires_at, courtesy_reason
- created_at, updated_at
```

**Remover da tabela `organizations`:**
- Todos os campos ASAAS (mover para tabelas específicas)

#### **2.2 Models Corretos**

```php
// ✅ AsaasAccount Model
class AsaasAccount extends Model {
    protected $fillable = ['organization_id', 'asaas_account_id', 'asaas_api_key', 'asaas_environment', 'is_active'];
    
    public function organization(): BelongsTo {
        return $this->belongsTo(Organization::class);
    }
    
    public function subscriptions(): HasMany {
        return $this->hasMany(AsaasSubscription::class);
    }
}

// ✅ AsaasSubscription Model (refatorado)
class AsaasSubscription extends Model {
    protected $fillable = ['organization_id', 'asaas_account_id', 'status', 'value', 'started_at', 'expires_at', 'is_courtesy', 'courtesy_expires_at', 'courtesy_reason'];
    
    public function organization(): BelongsTo {
        return $this->belongsTo(Organization::class);
    }
    
    public function account(): BelongsTo {
        return $this->belongsTo(AsaasAccount::class, 'asaas_account_id');
    }
}
```

### **FASE 3: Factories Funcionais**

#### **3.1 Factories Simples e Funcionais**

```php
// ✅ AsaasAccountFactory
class AsaasAccountFactory {
    public function buildFromModel(?AsaasAccountModel $model): ?AsaasAccount {
        if (!$model) return null;
        
        return new AsaasAccount(
            id: $model->id,
            organization_id: $model->organization_id,
            asaas_account_id: $model->asaas_account_id,
            asaas_api_key: $model->asaas_api_key,
            environment: AsaasEnvironment::from($model->asaas_environment),
            is_active: $model->is_active,
        );
    }
}

// ✅ AsaasSubscriptionFactory  
class AsaasSubscriptionFactory {
    public function buildFromModel(?AsaasSubscriptionModel $model): ?AsaasSubscription {
        if (!$model) return null;
        
        return new AsaasSubscription(
            id: $model->id,
            organization_id: $model->organization_id,
            status: SubscriptionStatus::from($model->status),
            value: $model->value,
            expires_at: $model->expires_at,
            is_courtesy: $model->is_courtesy,
            courtesy_expires_at: $model->courtesy_expires_at,
        );
    }
}
```

### **FASE 4: Use Cases Corretos**

#### **4.1 Use Cases com Responsabilidades Claras**

```php
// ✅ CreateAsaasAccount UseCase
class CreateAsaasAccount {
    public function perform(OrganizationDomain $organization, AsaasEnvironment $environment): AsaasAccount {
        // 1. Criar conta no ASAAS via API
        // 2. Salvar AsaasAccount no banco
        // 3. Retornar AsaasAccount domain
    }
}

// ✅ CreateSubscription UseCase
class CreateSubscription {
    public function perform(AsaasAccount $account, float $value): AsaasSubscription {
        // 1. Criar subscription no ASAAS via API
        // 2. Salvar AsaasSubscription no banco  
        // 3. Retornar AsaasSubscription domain
    }
}

// ✅ CheckSystemAccess UseCase
class CheckSystemAccess {
    public function perform(OrganizationDomain $organization): bool {
        // 1. Buscar AsaasSubscription da organização
        // 2. Verificar se pode acessar sistema
        // 3. Retornar boolean
    }
}
```

---

## 📊 **CRONOGRAMA DE IMPLEMENTAÇÃO**

### **Semana 1: Análise e Preparação**
- [ ] Backup completo do código atual
- [ ] Documentar todos os use cases atuais
- [ ] Mapear dependências entre classes
- [ ] Criar testes de regressão

### **Semana 2: Reorganização de Domains**
- [ ] Criar novos domains (AsaasAccount, AsaasSubscription refatorado)
- [ ] Simplificar AsaasOrganization domain
- [ ] Criar factories funcionais
- [ ] Testes unitários para domains

### **Semana 3: Reorganização de Persistence**
- [ ] Criar migrations para novas tabelas
- [ ] Migrar dados existentes
- [ ] Criar models corretos
- [ ] Testes de integração

### **Semana 4: Refatoração de Use Cases**
- [ ] Refatorar use cases existentes
- [ ] Corrigir imports e namespaces
- [ ] Atualizar controllers
- [ ] Testes end-to-end

### **Semana 5: Validação e Cleanup**
- [ ] Executar todos os testes
- [ ] Remover código obsoleto
- [ ] Documentação atualizada
- [ ] Deploy em ambiente de teste

---

## ⚠️ **RISCOS E MITIGAÇÕES**

### **Risco Alto: Breaking Changes**
- **Mitigação**: Manter APIs públicas compatíveis durante transição
- **Estratégia**: Feature flags para alternar entre implementações

### **Risco Médio: Perda de Dados**
- **Mitigação**: Migrations cuidadosas com rollback
- **Estratégia**: Testes extensivos em ambiente de desenvolvimento

### **Risco Baixo: Performance**
- **Mitigação**: Benchmarks antes e depois
- **Estratégia**: Otimizações pontuais se necessário

---

## 🎯 **RESULTADO ESPERADO**

### **Antes (Atual):**
- ❌ Impossível instanciar domains
- ❌ Factories quebradas
- ❌ Responsabilidades confusas
- ❌ AsaasSubscription não usado
- ❌ Imports incorretos

### **Depois (Reorganizado):**
- ✅ Domains simples e funcionais
- ✅ Factories que funcionam
- ✅ Responsabilidades claras
- ✅ AsaasSubscription usado corretamente
- ✅ Imports corretos e consistentes
- ✅ Código testável e manutenível

---

## 💡 **RECOMENDAÇÃO**

**Parar desenvolvimento atual** e implementar esta reorganização **IMEDIATAMENTE**. O estado atual do serviço ASAAS está causando mais problemas que soluções, e continuar desenvolvendo sobre esta base defeituosa só aumentará a dívida técnica.

A reorganização proposta resolverá os problemas fundamentais e criará uma base sólida para desenvolvimento futuro.
