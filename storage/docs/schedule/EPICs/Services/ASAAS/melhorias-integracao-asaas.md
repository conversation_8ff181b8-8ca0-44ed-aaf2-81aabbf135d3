# Epic: Melhorias e Correções na Integração ASAAS

## Overview

A integração ASAAS atual possui funcionalidades básicas implementadas, mas apresenta alguns problemas técnicos e limitações que impedem seu uso flexível em diferentes cenários de negócio. Esta Epic visa corrigir problemas identificados e implementar melhorias que tornarão a integração mais robusta e flexível.

### Funcionalidades Atuais Identificadas:

- ✅ **Criação automática de subconta ASAAS** - Funciona mas é sempre obrigatória
- ✅ **Criação automática de clientes ASAAS** - Funciona mas é sempre obrigatória  
- ✅ **Criação automática de cobranças ASAAS** - Funciona mas é sempre obrigatória
- ✅ **Sincronização de status de pagamentos** - Funciona corretamente
- ✅ **Verificação de assinatura e cortesia** - Funciona corretamente
- ⚠️ **Namespaces incorretos** - Algumas classes com imports errados
- ❌ **Controle opcional de integração** - Não existe flag para controlar criação automática
- ❌ **Endpoints para criação manual** - Não existem endpoints para criar integrações posteriormente
- ❌ **Endpoint para cortesia** - Não existe endpoint para gerar cortesias

### Melhorias Propostas:

#### 1. **Correção de Namespaces**
Corrigir imports incorretos em classes ASAAS, um exemplo é o use case `CreateSubaccount` que está importando `OrganizationDomain` com namespace errado. Mas com certeza há outros exemplos

#### 2. **Controle Opcional de Integração ASAAS**
Implementar flags booleanas para controlar quando criar automaticamente subcontas, clientes e cobranças no ASAAS, permitindo que organizações escolham não usar o ASAAS.

#### 3. **Endpoints para Criação Manual**
Criar endpoints e use cases para permitir criação manual de integrações ASAAS para organizações, clientes e vendas que já existem no sistema.

#### 4. **Sistema de Cortesia**
Implementar endpoint para gerar cortesias de assinatura com tempo de expiração configurável.

#### 5. **Padronização de Rotas**
Organizar novos endpoints seguindo padrão REST com prefixo `/api/asaas/`.

## Resumo do Plano de Implementação

A Epic será implementada em 4 fases sequenciais, cada uma focada em um aspecto específico das melhorias:

**Fase 1**: Correção de Namespaces - Auditoria completa e correção de todos os imports incorretos nas classes ASAAS
**Fase 2**: Flags de Controle - Implementar parâmetros opcionais nos requests para controlar criação automática
**Fase 3**: Endpoints Manuais - Criar endpoints para integração manual de organizações, clientes e vendas
**Fase 4**: Sistema de Cortesia Independente - Implementar cortesia na tabela organizations independente do ASAAS

## Plano de Implementação Detalhado

### 1. Auditoria e Correção de Namespaces

#### Problemas Identificados:
- `app/Services/ASAAS/UseCases/Organizations/CreateSubaccount.php` - Import incorreto: `use App\Domains\Organization\Organization as OrganizationDomain;`
- `app/Services/ASAAS/UseCases/Organizations/IsAllowedToUseSystem.php` - Possível inconsistência de tipos
- Testes anteriormente falhando por namespaces incorretos (já corrigidos)
- Possíveis outros imports incorretos em use cases, repositories e factories

#### Auditoria Completa Necessária:
- **Use Cases**: Verificar todos os imports de domínios em `app/Services/ASAAS/UseCases/`
- **Repositories**: Verificar imports de models e domains em `app/Services/ASAAS/Repositories/`
- **Factories**: Verificar imports de domains em `app/Services/ASAAS/Factories/`
- **Controllers**: Verificar imports em controllers que usam ASAAS
- **Commands**: Verificar imports em `app/Console/Commands/ASAAS/`

#### Correções Específicas:
- Corrigir `use App\Domains\Organization\Organization as OrganizationDomain;` para `use App\Domains\Organization as OrganizationDomain;`
- Verificar se todos os imports de `ClientDomain` apontam para `App\Domains\Inventory\Client`
- Verificar se todos os imports de `SaleDomain` apontam para `App\Domains\Inventory\Sale`
- Garantir consistência entre type hints e imports

### 2. Controle Opcional via Parâmetros de Request

#### Implementação:
- **Parâmetro `enable_asaas_integration`**: Adicionar nos requests de store relevantes (Client, Organization, Sale) (default: false)
- **Lógica nos Use Cases**: Verificar parâmetro antes de executar integrações ASAAS
- **Sem alterações no banco**: Não criar migrations, apenas lógica condicional

#### Requests Modificados:
- **OrganizationStoreRequest**: Adicionar campo opcional `enable_asaas_integration`
- **ClientStoreRequest**: Adicionar campo opcional `enable_asaas_integration`
- **SaleStoreRequest**: Adicionar campo opcional `enable_asaas_integration`

#### Use Cases Modificados:
- **Organization/Store**: Verificar `$request->enable_asaas_integration` antes de chamar `CreateSubaccount`
- **Client/Store**: Verificar `$request->enable_asaas_integration` antes de chamar `CreateCustomer`
- **Sale/Store**: Verificar `$request->enable_asaas_integration` antes de chamar `CreatePayment`

#### Controllers Modificados:
- **OrganizationController::store()**: Passar flag para use case
- **ClientController::store()**: Passar flag para use case
- **SaleController::store()**: Passar flag para use case

### 3. Endpoints para Criação Manual de Integrações

#### Rotas/Endpoints Necessários:
- `POST /api/asaas/organization/create-subaccount` - Criar subconta ASAAS para organização existente
- `POST /api/asaas/client/create-customer` - Criar customer ASAAS para cliente existente
- `POST /api/asaas/sale/create-payment` - Criar payment ASAAS para venda existente
- `GET /api/asaas/organization/{id}/status` - Verificar status da integração da organização
- `GET /api/asaas/client/{id}/status` - Verificar status da integração do cliente
- `GET /api/asaas/sale/{id}/status` - Verificar status da integração da venda

#### Use Cases:
- **ASAAS/Organizations/CreateSubaccountManual**: Reutilizar `CreateSubaccount` existente
- **ASAAS/Clients/CreateCustomerManual**: Reutilizar `CreateCustomer` existente
- **ASAAS/Sales/CreatePaymentManual**: Reutilizar `CreatePayment` existente
- **ASAAS/Organizations/GetIntegrationStatus**: Verificar status da integração da organização
- **ASAAS/Clients/GetIntegrationStatus**: Verificar status da integração do cliente
- **ASAAS/Sales/GetIntegrationStatus**: Verificar status da integração da venda

#### Controllers:
- **ASAAS/OrganizationController**: Controller específico para operações ASAAS de organizações
- **ASAAS/ClientController**: Controller específico para operações ASAAS de clientes
- **ASAAS/SaleController**: Controller específico para operações ASAAS de vendas

#### Requests:
- **ASAAS/Organization/CreateSubaccountRequest**: Validação para criação manual de subconta
- **ASAAS/Client/CreateCustomerRequest**: Validação para criação manual de customer
- **ASAAS/Sale/CreatePaymentRequest**: Validação para criação manual de payment

#### Validações:
- Verificar se organização/cliente/venda existe
- Verificar se já não possui integração ASAAS
- Verificar se organização tem dados necessários para ASAAS

### 4. Sistema de Cortesia Independente

#### Problema Atual:
- Cortesia está atrelada à `AsaasOrganization` (tabela `asaas_organizations`)
- Organizações sem conta ASAAS não podem ter cortesia
- Necessário permitir cortesia independente da integração ASAAS

#### Solução:
- **Adicionar campos na tabela `organizations`**: `is_courtesy`, `courtesy_expires_at`, `courtesy_reason`
- **Manter campos em `asaas_organizations`**: Para organizações com ASAAS
- **Lógica híbrida**: Verificar cortesia em ambas as tabelas

#### Database:
- **Migration**: Adicionar campos de cortesia na tabela `organizations`
- **Campos**: `is_courtesy` (boolean), `courtesy_expires_at` (date), `courtesy_reason` (text)

#### Domínios:
- **Organization**: Adicionar métodos `isInCourtesy()`, `canAccessSystem()`
- **Lógica**: Verificar cortesia local OU cortesia ASAAS

#### Use Cases:
- **Organization/GrantCourtesy**: Gerar cortesia na tabela `organizations`
- **Organization/GrantAsaasCourtesy**: Gerar cortesia na tabela `asaas_organizations` (existente)
- **Organization/CheckAccess**: Verificar acesso considerando ambas as cortesias

#### Rotas/Endpoints:
- `POST /api/asaas/organization/grant-courtesy` - Gerar cortesia independente
- `POST /api/asaas/organization/grant-asaas-courtesy` - Gerar cortesia ASAAS
- `GET /api/asaas/organization/{id}/courtesy-status` - Verificar status de cortesia

#### Requests:
- **ASAAS/Organization/GrantCourtesyRequest**: Validação para cortesia independente

## Previsão de PR

### Migrations
```
database/migrations/xxxx_add_courtesy_fields_to_organizations_table.php (novo)
```

### Domains
```
app/Domains/Organization.php (modificado - adicionar métodos de cortesia)
```

### Use Cases
```
app/Services/ASAAS/UseCases/Organizations/CreateSubaccount.php (modificado - corrigir namespace)
app/Services/ASAAS/UseCases/Organizations/GetIntegrationStatus.php (novo)
app/Services/ASAAS/UseCases/Organizations/GrantCourtesy.php (novo)
app/Services/ASAAS/UseCases/Organizations/CheckAccess.php (novo)
app/Services/ASAAS/UseCases/Clients/GetIntegrationStatus.php (novo)
app/Services/ASAAS/UseCases/Sales/GetIntegrationStatus.php (novo)
app/UseCases/Organization/Store.php (modificado - verificar parâmetro)
app/UseCases/Inventory/Client/Store.php (modificado - verificar parâmetro)
app/UseCases/Inventory/Sale/Store.php (modificado - verificar parâmetro)
```

### Controllers
```
app/Http/Controllers/ASAAS/OrganizationController.php (novo)
app/Http/Controllers/ASAAS/ClientController.php (novo)
app/Http/Controllers/ASAAS/SaleController.php (novo)
```

### Requests
```
app/Http/Requests/ASAAS/Organization/CreateSubaccountRequest.php (novo)
app/Http/Requests/ASAAS/Organization/GrantCourtesyRequest.php (novo)
app/Http/Requests/ASAAS/Client/CreateCustomerRequest.php (novo)
app/Http/Requests/ASAAS/Sale/CreatePaymentRequest.php (novo)
app/Http/Requests/Organization/StoreRequest.php (modificado - adicionar enable_asaas_integration)
app/Http/Requests/Client/StoreRequest.php (modificado - adicionar enable_asaas_integration)
app/Http/Requests/Sale/StoreRequest.php (modificado - adicionar enable_asaas_integration)
```

### Routes
```
routes/api.php (modificado - adicionar rotas ASAAS)
```

**Total Estimado: ~18 arquivos (11 novos + 7 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Namespaces Incorretos
**Situação Atual**: Classes ASAAS com imports incorretos causando erros de "classe indefinida"
**Impacto**: Falhas na execução de use cases e possíveis erros em produção
**Solução**: Corrigir todos os imports incorretos seguindo a estrutura de namespaces do projeto

### Problema 2: Integração ASAAS Sempre Obrigatória
**Situação Atual**: Toda organização, cliente e venda criados automaticamente geram registros no ASAAS
**Impacto**: Organizações que não querem usar ASAAS são forçadas a ter integração
**Solução**: Implementar parâmetros opcionais nos requests para controlar quando criar integrações automaticamente

### Problema 3: Impossibilidade de Integração Posterior
**Situação Atual**: Não existem endpoints para criar integrações ASAAS para registros já existentes
**Impacto**: Organizações que decidem usar ASAAS depois não conseguem integrar dados existentes
**Solução**: Criar endpoints específicos para criação manual de integrações

### Problema 4: Cortesia Dependente de Integração ASAAS
**Situação Atual**: Cortesia só funciona para organizações com conta ASAAS criada
**Impacto**: Organizações sem ASAAS não podem receber cortesia, limitando estratégias comerciais
**Solução**: Implementar sistema de cortesia independente na tabela organizations

### Problema 5: Falta de Padronização de Rotas
**Situação Atual**: Endpoints ASAAS misturados com endpoints principais
**Impacto**: Dificuldade de organização e manutenção das rotas
**Solução**: Criar estrutura padronizada com prefixo `/api/asaas/`

## Plano de Testes

### Testes Unitários:
- Validação de flags de controle de integração
- Testes de namespaces corrigidos
- Validação de regras de negócio para cortesia
- Testes de criação manual de integrações

### Testes de Integração:
- Fluxo completo de criação manual de subconta
- Fluxo completo de criação manual de customer
- Fluxo completo de criação manual de payment
- Integração com API ASAAS para todos os novos endpoints

### Testes de Regressão:
- Funcionalidades existentes de criação automática devem continuar funcionando
- Sincronização de status deve continuar funcionando
- Verificação de assinatura deve continuar funcionando
- Comandos existentes devem continuar funcionando

## Conclusão

Esta Epic resolverá problemas técnicos críticos na integração ASAAS e implementará funcionalidades essenciais para flexibilizar o uso do sistema. As melhorias permitirão que organizações tenham controle total sobre quando e como usar a integração ASAAS.

### Benefícios Esperados:
- **Técnicos**: Correção de namespaces eliminará erros de execução
- **Flexibilidade**: Organizações poderão escolher usar ou não o ASAAS
- **Escalabilidade**: Possibilidade de integrar dados existentes posteriormente
- **Negócio**: Capacidade de gerar cortesias estratégicas

### Impacto no Negócio:
- Redução de erros técnicos em produção
- Maior flexibilidade para onboarding de clientes
- Possibilidade de estratégias comerciais com cortesias
- Melhor organização e manutenibilidade do código

## Sugestões de Melhorias Futuras

### 6. **Webhooks ASAAS Inteligentes**
Implementar sistema de webhooks mais robusto que processe automaticamente mudanças de status, estornos e outras notificações do ASAAS, reduzindo a necessidade de sincronização manual.

### 7. **Dashboard de Métricas ASAAS**
Criar dashboard específico para métricas ASAAS com gráficos de receita, inadimplência, conversão de pagamentos e performance por organização.

### 8. **Split de Pagamentos Automático**
Implementar sistema de split automático onde uma porcentagem de cada pagamento vai automaticamente para nossa conta master, facilitando a cobrança de taxas.

### 9. **Assinaturas Recorrentes Inteligentes**
Desenvolver sistema de assinaturas recorrentes com diferentes planos, upgrades/downgrades automáticos e gestão de ciclos de cobrança.

### 10. **Análise de Risco e IA**
Implementar sistema de análise de risco usando IA para identificar padrões de inadimplência e sugerir ações preventivas para organizações.

### 11. **Integração com Contabilidade**
Criar exportadores automáticos para sistemas contábeis populares (ContaAzul, Omie, etc.) facilitando a conciliação financeira.

### 12. **API Pública ASAAS**
Desenvolver API pública que permita integrações externas com nosso sistema ASAAS, possibilitando que clientes criem suas próprias integrações.

### 13. **Marketplace de Pagamentos**
Implementar funcionalidade de marketplace onde organizações podem vender produtos de outras organizações com split automático de comissões.

### 14. **Notificações Personalizadas**
Sistema para personalizar emails e SMS de cobrança com identidade visual da organização, melhorando a experiência do cliente final.

### 15. **Gestão Avançada de Estornos**
Interface completa para gestão de estornos, reembolsos parciais, cancelamentos e disputas diretamente pelo sistema.

## Referências

- Documentação atual do serviço ASAAS - `storage/docs/services/ASAAS/README.md`
- Estrutura atual da integração - `app/Services/ASAAS/`
- API ASAAS - https://docs.asaas.com/
- Template de Epic - `storage/docs/schedule/EPICs/epic-template.md`
