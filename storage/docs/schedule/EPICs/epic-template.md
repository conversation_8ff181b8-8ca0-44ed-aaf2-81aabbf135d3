# Epic: [TÍTULO DA EPIC]

> **Instruções para o Título**: Use um título claro e descritivo que capture a essência da Epic. Formato sugerido: "Epic: [Funcionalidade Principal] - [Objetivo/Melhoria]". Exemplo: "Epic: Sistema de Notificações - Implementação e Melhorias"

## Overview

> **Descrição da Seção**: Esta seção deve fornecer uma visão geral completa da Epic, explicando o contexto atual, os problemas identificados e a visão geral da solução proposta. Deve ser compreensível para stakeholders técnicos e não-técnicos.

[Descreva aqui o contexto geral da Epic, explicando o estado atual do sistema e por que esta Epic é necessária. Inclua uma breve descrição do que será implementado.]

### Funcionalidades Atuais Identificadas:
> **Instruções**: Liste todas as funcionalidades existentes relacionadas ao escopo da Epic. Use ✅ para funcionalidades que já existem e funcionam corretamente, ⚠️ para funcionalidades que existem mas precisam de melhorias, e ❌ para funcionalidades que não existem mas são necessárias.

- ✅ [Funcionalidade existente 1]
- ✅ [Funcionalidade existente 2]
- ⚠️ [Funcionalidade que precisa de melhoria]
- ❌ [Funcionalidade necessária mas inexistente]

### Melhorias Propostas:
> **Instruções**: Descreva as principais melhorias que serão implementadas. Organize em categorias lógicas e seja específico sobre o que cada melhoria resolverá.

#### 1. **[Nome da Melhoria 1]**
[Descrição detalhada da primeira melhoria, explicando o problema que resolve e como será implementada]

#### 2. **[Nome da Melhoria 2]**
[Descrição detalhada da segunda melhoria, explicando o problema que resolve e como será implementada]

#### 3. **[Nome da Melhoria 3]**
[Continue listando todas as melhorias principais...]

## Resumo do Plano de Implementação

> **Descrição da Seção**: Esta seção deve apresentar um resumo executivo do plano de implementação, dividindo a Epic em fases lógicas e sequenciais. Cada fase deve ser independente e entregável, permitindo validação incremental.

> **Instruções**:
> - Divida a Epic em 3-7 fases lógicas
> - Cada fase deve ter um objetivo claro e entregável
> - Ordene as fases por dependência e prioridade
> - Use uma frase concisa para descrever cada fase

[Descreva brevemente como a Epic será implementada, quantas fases terá e a lógica por trás da divisão]

**Fase 1**: [Nome da Fase] - [Descrição concisa do que será implementado]
**Fase 2**: [Nome da Fase] - [Descrição concisa do que será implementado]
**Fase 3**: [Nome da Fase] - [Descrição concisa do que será implementado]
**Fase 4**: [Nome da Fase] - [Descrição concisa do que será implementado]
**Fase 5**: [Nome da Fase] - [Descrição concisa do que será implementado]

## Plano de Implementação Detalhado

> **Descrição da Seção**: Esta é a seção mais técnica e detalhada da Epic. Aqui você deve detalhar cada fase da implementação, especificando todos os componentes técnicos necessários: rotas, banco de dados, domínios, use cases, jobs, etc.

> **Instruções**:
> - Para cada fase, inclua todas as subseções relevantes: Rotas/Endpoints, Database, Domínios, Usecases, Jobs/Cron
> - Seja específico sobre nomes de arquivos, tabelas, campos e métodos
> - Inclua validações e regras de negócio importantes
> - Mantenha consistência com os padrões arquiteturais do projeto

### 1. [Nome da Primeira Fase]

> **Instruções para cada fase**: Descreva o objetivo da fase e liste todos os componentes técnicos necessários

#### Rotas/Endpoints Necessários:
> **Instruções**: Liste todas as rotas que precisam ser criadas ou modificadas, incluindo método HTTP, path e descrição
- `POST /api/[resource]` - [Descrição da funcionalidade]
- `GET /api/[resource]` - [Descrição da funcionalidade]
- `PUT /api/[resource]/{id}` - [Descrição da funcionalidade]
- `DELETE /api/[resource]/{id}` - [Descrição da funcionalidade]

#### Database:
> **Instruções**: Descreva todas as tabelas que precisam ser criadas ou modificadas, incluindo campos principais e relacionamentos
- **Tabela `[nome_tabela]`**: [Descrição do propósito]. Campos: [liste os campos principais com tipos]
- **Tabela `[nome_tabela_relacionamento]`**: [Descrição do relacionamento]. Campos: [liste os campos]

#### Domínios:
> **Instruções**: Liste todos os domínios que precisam ser criados ou modificados, com breve descrição de responsabilidade
- **[NomeDominio]**: [Descrição da responsabilidade do domínio]
- **[OutroDominio]**: [Descrição da responsabilidade do domínio]

#### Usecases:
> **Instruções**: Liste todos os use cases necessários, organizados por domínio/funcionalidade
- **[Dominio]/[Acao]**: [Descrição do que o use case faz]
- **[Dominio]/[Acao]**: [Descrição do que o use case faz]

#### Jobs/Cron (se aplicável):
> **Instruções**: Liste jobs que precisam ser criados, incluindo frequência de execução
- **[NomeJob]**: [Descrição do job e quando deve executar]

### 2. [Nome da Segunda Fase]

> **Instruções**: Continue o padrão da primeira fase para todas as fases subsequentes

#### [Subseção Relevante]:
> **Instruções**: Use as subseções apropriadas para cada fase (Rotas/Endpoints, Database, Domínios, Usecases, Jobs/Cron, etc.)

[Continue seguindo o mesmo padrão detalhado da primeira fase...]

### 3. [Nome da Terceira Fase]

[Continue o padrão...]

### [Continue para todas as fases necessárias...]

## Previsão de PR

> **Descrição da Seção**: Esta seção deve mapear todos os arquivos que precisam ser criados ou modificados para implementar a Epic. É uma lista exaustiva que serve como checklist para desenvolvimento e revisão de código.

> **Instruções**:
> - Organize por tipo de arquivo (Enums, Models, Domains, etc.)
> - Indique se o arquivo é "novo" ou "modificado"
> - Seja específico sobre caminhos e nomes de arquivos
> - Inclua uma estimativa total de arquivos no final
> - Use a estrutura de diretórios do projeto

### Enums
```
app/Enums/[NomeEnum].php (novo)
app/Enums/[OutroEnum].php (modificado)
```

### Models
```
app/Models/[NomeModel].php (novo)
app/Models/[OutroModel].php (modificado)
```

### Domains
```
app/Domains/[Modulo]/[NomeDomain].php (novo)
app/Domains/[Modulo]/[OutroDomain].php (modificado)
```

### Factories
```
app/Factories/[Modulo]/[NomeFactory].php (novo)
app/Factories/[Modulo]/[OutroFactory].php (modificado)
```

### Repositories
```
app/Repositories/[NomeRepository].php (novo)
app/Repositories/[OutroRepository].php (modificado)
```

### Use Cases
```
app/UseCases/[Modulo]/[Dominio]/[Acao].php (novo)
app/UseCases/[Modulo]/[Dominio]/[OutraAcao].php (novo)
```

### Controllers
```
app/Http/Controllers/[Modulo]/[NomeController].php (novo)
app/Http/Controllers/[Modulo]/[OutroController].php (modificado)
```

### Requests
```
app/Http/Requests/[Dominio]/[TipoRequest].php (novo)
```

### Jobs
```
app/Jobs/[NomeJob].php (novo)
```

### Migrations
```
database/migrations/xxxx_create_[nome_tabela]_table.php (novo)
database/migrations/xxxx_add_[campos]_to_[tabela]_table.php (novo)
```

### Routes
```
routes/api.php (modificado - adicionar novas rotas)
```

### Console
```
app/Console/Kernel.php (modificado - adicionar jobs ao schedule)
```

### Filters (se aplicável)
```
app/Domains/Filters/[NomeFilter].php (novo)
app/Domains/Filters/[OutroFilter].php (modificado)
```

**Total Estimado: ~[X] arquivos ([Y] novos + [Z] modificados)**


## Melhorias Específicas Identificadas

> **Descrição da Seção**: Esta seção deve identificar e documentar os problemas específicos que a Epic resolve. Para cada problema, descreva a situação atual, o impacto negativo e a solução proposta.

> **Instruções**:
> - Liste 3-7 problemas principais que a Epic resolve
> - Para cada problema, use o formato: Situação Atual → Impacto → Solução
> - Seja específico sobre como cada problema afeta o usuário/negócio
> - Conecte cada problema às melhorias propostas na seção Overview

### Problema 1: [Nome do Problema]
**Situação Atual**: [Descreva o estado atual do sistema que causa o problema]
**Impacto**: [Explique como isso afeta usuários, performance, ou negócio]
**Solução**: [Descreva brevemente como a Epic resolve este problema]

### Problema 2: [Nome do Problema]
**Situação Atual**: [Descreva o estado atual do sistema que causa o problema]
**Impacto**: [Explique como isso afeta usuários, performance, ou negócio]
**Solução**: [Descreva brevemente como a Epic resolve este problema]

### Problema 3: [Nome do Problema]
**Situação Atual**: [Descreva o estado atual do sistema que causa o problema]
**Impacto**: [Explique como isso afeta usuários, performance, ou negócio]
**Solução**: [Descreva brevemente como a Epic resolve este problema]

[Continue para todos os problemas identificados...]

## Plano de Testes

> **Descrição da Seção**: Esta seção deve detalhar a estratégia de testes para garantir que a Epic seja implementada com qualidade e não quebre funcionalidades existentes.

> **Instruções**:
> - Organize os testes por tipo (Unitários, Integração, Performance, Regressão)
> - Para cada tipo, liste os cenários específicos que devem ser testados
> - Inclua critérios de aceitação quando relevante
> - Considere testes de edge cases e cenários de falha

### Testes Unitários:
> **Instruções**: Liste os componentes individuais que precisam de testes unitários
- [Funcionalidade específica a ser testada]
- [Validação de regras de negócio]
- [Cálculos e transformações de dados]
- [Validações de entrada e saída]

### Testes de Integração:
> **Instruções**: Liste as integrações entre componentes que precisam ser testadas
- [Integração com APIs externas]
- [Fluxos completos entre domínios]
- [Processamento de jobs e filas]
- [Webhooks e callbacks]

### Testes de Performance:
> **Instruções**: Liste cenários de carga e performance que devem ser validados
- [Cenários de alto volume]
- [Testes de timeout e latência]
- [Uso de memória e recursos]
- [Concorrência e paralelismo]

### Testes de Regressão:
> **Instruções**: Liste funcionalidades existentes que devem continuar funcionando
- [Funcionalidades existentes que não devem ser afetadas]
- [Compatibilidade com dados existentes]
- [Migração de dados sem perda]
- [APIs existentes mantendo compatibilidade]

## Conclusão

> **Descrição da Seção**: Esta seção deve resumir o valor e impacto esperado da Epic, consolidando os benefícios para usuários e negócio.

> **Instruções**:
> - Resuma o valor geral que a Epic trará
> - Liste benefícios específicos organizados por categoria
> - Inclua métricas de sucesso quando possível
> - Conecte os benefícios aos problemas identificados anteriormente

[Escreva um parágrafo resumindo o impacto geral da Epic e como ela transformará o sistema atual]

### Benefícios Esperados:
> **Instruções**: Organize os benefícios por categoria (técnicos, de negócio, de usuário)
- **[Categoria de Benefício]**: [Descrição específica do benefício]
- **[Categoria de Benefício]**: [Descrição específica do benefício]
- **[Categoria de Benefício]**: [Descrição específica do benefício]
- **[Categoria de Benefício]**: [Descrição específica do benefício]

### Impacto no Negócio:
> **Instruções**: Liste impactos mensuráveis no negócio, incluindo métricas quando possível
- [Impacto específico no negócio com métrica se possível]
- [Melhoria em processo ou eficiência]
- [Redução de custos ou aumento de receita]
- [Melhoria na experiência do usuário]

### Métricas de Sucesso (opcional):
> **Instruções**: Se aplicável, liste métricas que indicarão o sucesso da Epic
- [Métrica específica e valor esperado]
- [KPI que será impactado]
- [Tempo de resposta ou performance esperada]

## Referências

> **Descrição da Seção**: Esta seção deve listar todas as referências técnicas, documentações e recursos utilizados para planejar a Epic.

> **Instruções**:
> - Liste documentações oficiais de APIs e frameworks utilizados
> - Inclua artigos técnicos ou best practices relevantes
> - Adicione links para RFCs ou especificações técnicas
> - Referencie outras Epics ou documentos internos relacionados

- [Nome da Documentação/Recurso] - [URL se aplicável]
- [Framework/API Documentation] - [URL se aplicável]
- [Best Practices Guide] - [URL se aplicável]
- [Especificação Técnica] - [URL se aplicável]
- [Epic/Documento Relacionado] - [Caminho interno se aplicável]
