# Epic: ChatBot Sistema de Escalação Humana

## 📋 Overview

Esta Epic implementa um sistema completo de **escalação para atendimento humano**, permitindo que conversas do ChatBot sejam transferidas para atendentes reais quando necessário. O sistema inclui fila de atendimento, notificações, e interface para atendentes.

**Pré-requisitos:** Esta Epic assume que as seguintes já estão implementadas:
- [Epic: ChatBot Core Improvements](./chatbot-core-improvements.md)
- [Epic: Integração com Resend Email](../Core/integracao-servico-email.md)

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ✅ | Sistema base de conversas e interações |
| ✅ | Processamento de mensagens WhatsApp |
| ✅ | Domains de Conversation e Interaction |
| ✅ | Sistema de notificações via Resend |
| ❌ | Escalação automática para humanos |
| ❌ | Fila de atendimento |
| ❌ | Interface para atendentes |
| ❌ | Notificações de escalação |
| ❌ | Transferência de contexto |
| ❌ | Métricas de atendimento humano |

## 🚀 Melhorias Propostas

### 1. Sistema de escalação automática
Detectar quando uma conversa precisa ser escalada (palavras-chave, falhas repetidas, solicitação direta) e transferir automaticamente para fila humana.

### 2. Fila de atendimento
Sistema de fila que distribui conversas escaladas para atendentes disponíveis, com priorização e balanceamento de carga.

### 3. Interface para atendentes
Dashboard web para atendentes visualizarem conversas escaladas, histórico completo e responderem via interface unificada.

### 4. Notificações inteligentes
Sistema de notificações via email (Resend) e push para alertar atendentes sobre novas escalações e mensagens urgentes.

### 5. Transferência de contexto
Preservar todo o contexto da conversa automatizada para que o atendente humano tenha visibilidade completa do histórico.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Implementar sistema de escalação e domains relacionados
- **Fase 2:** Criar fila de atendimento e distribuição
- **Fase 3:** Desenvolver interface web para atendentes
- **Fase 4:** Implementar notificações via Resend
- **Fase 5:** Adicionar métricas e relatórios de atendimento

## 🔧 Plano de Implementação Detalhado

### 1. Sistema de Escalação

**Novos Domains:**
```php
// app/Domains/ChatBot/HumanEscalation.php
class HumanEscalation 
{
    public ?int $id;
    public ?int $conversation_id;
    public ?int $organization_id;
    public ?int $escalated_by_user_id;
    public ?int $assigned_to_user_id;
    public ?string $escalation_reason;
    public ?string $escalation_type; // AUTO, MANUAL, KEYWORD, FAILURE
    public ?string $status; // PENDING, ASSIGNED, IN_PROGRESS, RESOLVED, CLOSED
    public ?string $priority; // LOW, MEDIUM, HIGH, URGENT
    public ?Carbon $escalated_at;
    public ?Carbon $assigned_at;
    public ?Carbon $resolved_at;
    public ?string $resolution_notes;
    public ?Conversation $conversation;
    public ?User $escalated_by;
    public ?User $assigned_to;
}
```

**Escalation Triggers:**
```php
enum EscalationType: string 
{
    case AUTO_KEYWORD = 'auto_keyword';      // Palavras como "atendente", "humano"
    case AUTO_FAILURE = 'auto_failure';      // Múltiplas falhas no fluxo
    case AUTO_TIMEOUT = 'auto_timeout';      // Timeout em step crítico
    case MANUAL_REQUEST = 'manual_request';  // Usuário solicita diretamente
    case COMMAND_TRIGGER = 'command_trigger'; // Command step específico
}
```

**UseCases de Escalação:**
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/EscalateConversation.php`
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/DetectEscalationTriggers.php`
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/AssignToAgent.php`

### 2. Fila de Atendimento

**Queue Management Domain:**
```php
// app/Domains/ChatBot/AttendantQueue.php
class AttendantQueue 
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id; // Atendente
    public ?string $status; // AVAILABLE, BUSY, AWAY, OFFLINE
    public ?int $max_concurrent_chats;
    public ?int $current_chat_count;
    public ?Carbon $last_activity;
    public ?array $skills; // Tags de especialização
    public ?int $priority_level;
}
```

**Queue Distribution Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/QueueDistributionService.php`

**Distribution Strategies:**
- **Round Robin:** Distribui igualmente entre atendentes
- **Least Busy:** Atribui para quem tem menos conversas
- **Skill Based:** Considera especialização do atendente
- **Priority Based:** Atendentes com maior prioridade primeiro

### 3. Interface para Atendentes

**Controllers:**
- `app/Http/Controllers/ChatBot/AttendantDashboardController.php`
- `app/Http/Controllers/ChatBot/ConversationController.php`
- `app/Http/Controllers/ChatBot/EscalationController.php`

**Rotas API:**
```php
// Adicionar em routes/api.php
Route::prefix('chatbot/attendant')->middleware(['auth:sanctum'])->group(function () {
    Route::get('dashboard', [AttendantDashboardController::class, 'index']);
    Route::get('conversations', [ConversationController::class, 'index']);
    Route::get('conversations/{id}', [ConversationController::class, 'show']);
    Route::post('conversations/{id}/reply', [ConversationController::class, 'reply']);
    Route::post('conversations/{id}/close', [ConversationController::class, 'close']);
    Route::post('escalations/{id}/accept', [EscalationController::class, 'accept']);
    Route::post('escalations/{id}/resolve', [EscalationController::class, 'resolve']);
    Route::patch('status', [AttendantDashboardController::class, 'updateStatus']);
});
```

**Frontend Components (se aplicável):**
- Dashboard de conversas ativas
- Chat interface para responder
- Histórico completo da conversa
- Informações do cliente
- Ações rápidas (fechar, transferir, etc.)

### 4. Notificações via Resend

**Email Notifications:**
```php
// Usar sistema Resend já implementado
use App\Services\Resend\Domains\EscalationNotificationEmail;
use App\Services\Resend\Domains\NewMessageNotificationEmail;
```

**Templates de Email:**
- `resources/views/emails/escalation-notification.blade.php`
- `resources/views/emails/new-message-notification.blade.php`
- `resources/views/emails/escalation-resolved.blade.php`

**Notification Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/EscalationNotificationService.php`

### 5. Transferência de Contexto

**Context Transfer Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/ContextTransferService.php`

**Context Data Structure:**
```php
[
    'conversation_summary' => [
        'started_at' => Carbon,
        'flow_name' => string,
        'current_step' => string,
        'steps_completed' => int,
        'escalation_reason' => string
    ],
    'client_data' => [
        'name' => string,
        'phone' => string,
        'email' => string,
        'collected_data' => array
    ],
    'interaction_history' => [
        // Array de todas as interações
    ],
    'flow_context' => [
        'variables' => array,
        'last_inputs' => array,
        'failed_steps' => array
    ]
]
```

## 📦 Previsão de Arquivos do PR

### Migrations
```
database/migrations/xxxx_create_human_escalations_table.php (novo)
database/migrations/xxxx_create_attendant_queues_table.php (novo)
database/migrations/xxxx_add_escalation_fields_to_conversations_table.php (novo)
```

### Models
```
app/Models/ChatBot/HumanEscalation.php (novo)
app/Models/ChatBot/AttendantQueue.php (novo)
```

### Domains
```
app/Domains/ChatBot/HumanEscalation.php (novo)
app/Domains/ChatBot/AttendantQueue.php (novo)
```

### Factories
```
app/Factories/ChatBot/HumanEscalationFactory.php (novo)
app/Factories/ChatBot/AttendantQueueFactory.php (novo)
```

### Repositories
```
app/Repositories/ChatBot/HumanEscalationRepository.php (novo)
app/Repositories/ChatBot/AttendantQueueRepository.php (novo)
```

### Services
```
app/Services/Meta/WhatsApp/ChatBot/Services/QueueDistributionService.php (novo)
app/Services/Meta/WhatsApp/ChatBot/Services/EscalationNotificationService.php (novo)
app/Services/Meta/WhatsApp/ChatBot/Services/ContextTransferService.php (novo)
```

### UseCases
```
app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/EscalateConversation.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/DetectEscalationTriggers.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/AssignToAgent.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/Escalation/ResolveEscalation.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/AttendantDashboardController.php (novo)
app/Http/Controllers/ChatBot/ConversationController.php (novo)
app/Http/Controllers/ChatBot/EscalationController.php (novo)
```

### Requests
```
app/Http/Requests/ChatBot/ReplyToConversationRequest.php (novo)
app/Http/Requests/ChatBot/ResolveEscalationRequest.php (novo)
app/Http/Requests/ChatBot/UpdateAttendantStatusRequest.php (novo)
```

### Email Domains (Resend)
```
app/Services/Resend/Domains/EscalationNotificationEmail.php (novo)
app/Services/Resend/Domains/NewMessageNotificationEmail.php (novo)
app/Services/Resend/Domains/EscalationResolvedEmail.php (novo)
```

### Email Templates
```
resources/views/emails/escalation-notification.blade.php (novo)
resources/views/emails/new-message-notification.blade.php (novo)
resources/views/emails/escalation-resolved.blade.php (novo)
```

### Enums
```
app/Enums/ChatBot/EscalationType.php (novo)
app/Enums/ChatBot/EscalationStatus.php (novo)
app/Enums/ChatBot/AttendantStatus.php (novo)
```

### Routes (modificados)
```
routes/api.php (modificado - adicionar rotas de atendimento)
```

### Tests
```
tests/Feature/ChatBot/EscalationFlowTest.php (novo)
tests/Feature/ChatBot/AttendantDashboardTest.php (novo)
tests/Feature/ChatBot/QueueDistributionTest.php (novo)
tests/Feature/ChatBot/NotificationTest.php (novo)
tests/Unit/Services/ChatBot/ContextTransferTest.php (novo)
```

**Total estimado:** ~35 arquivos (34 novos + 1 modificado)

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Ausência de escalação para atendimento humano

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | ChatBot não consegue transferir para humanos |
| **Impacto** | Clientes ficam presos em loops quando precisam de ajuda humana |
| **Solução** | Sistema completo de escalação com triggers automáticos e manuais |

### Problema 2: Falta de interface para atendentes

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sem interface para atendentes gerenciarem conversas |
| **Impacto** | Impossível oferecer atendimento humano eficiente |
| **Solução** | Dashboard web com chat interface e gestão de fila |

### Problema 3: Ausência de notificações para escalações

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Atendentes não são notificados sobre escalações |
| **Impacto** | Demora no atendimento e perda de clientes |
| **Solução** | Sistema de notificações via Resend com templates específicos |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Detecção de triggers de escalação
- ✅ Distribuição de fila por diferentes estratégias
- ✅ Transferência de contexto completo
- ✅ Envio de notificações via Resend
- ✅ Atualização de status de atendentes

### Testes de Integração
- ✅ Fluxo completo de escalação automática
- ✅ Interface de atendente com conversas reais
- ✅ Notificações em tempo real
- ✅ Resolução de escalações
- ✅ Integração com sistema de email

### Testes de Regressão
- ✅ ChatBot automático continua funcionando
- ✅ Conversas não escaladas não são afetadas
- ✅ Webhook processing mantém performance
- ✅ Sistema de notificações não interfere com outros emails

## 🎯 Conclusão

O sistema de escalação humana completa a funcionalidade do ChatBot, permitindo uma transição suave entre atendimento automatizado e humano. A integração com Resend garante notificações profissionais, enquanto a interface web oferece uma experiência completa para os atendentes.

## 📈 Benefícios Esperados

### Técnicos
- ✅ Escalação automática baseada em triggers inteligentes
- ✅ Fila de atendimento com distribuição balanceada
- ✅ Interface web responsiva para atendentes
- ✅ Notificações via Resend integradas
- ✅ Preservação completa de contexto

### De Negócio
- 👥 Atendimento híbrido (bot + humano) eficiente
- 📧 Notificações profissionais via email
- 📊 Métricas de escalação e resolução
- ⚡ Redução do tempo de resposta

### De Usuário
- 🤖 Transição transparente para atendimento humano
- 💬 Contexto preservado na transferência
- ⏱️ Atendimento mais rápido quando necessário
- 🎯 Resolução eficaz de problemas complexos

## 💼 Impacto no Negócio

- 🏢 Atendimento completo sem perda de clientes
- 📈 Melhora na satisfação do cliente
- 🤖 Otimização do uso de recursos humanos
- 🔄 Base para métricas avançadas de atendimento

## 📚 Referências

- [Epic: ChatBot Core Improvements](./chatbot-core-improvements.md)
- [Epic: Integração com Resend Email](../Core/integracao-servico-email.md)
- [ChatBot Sistema Base](./chatbot-automatico.md)
- [Laravel Queues](https://laravel.com/docs/queues)
- [Real-time Broadcasting](https://laravel.com/docs/broadcasting)

---

**Próximos Passos:** Após implementação, seguir para [Epic 3: Agendamento Automático](./chatbot-scheduling.md).
