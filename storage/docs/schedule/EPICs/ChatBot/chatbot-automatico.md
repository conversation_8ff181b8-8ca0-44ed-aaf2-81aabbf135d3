# Epic: Sistema de ChatBot Automático - Visão Geral

## 📋 Overview

O sistema de ChatBot automático do Obvio já possui uma **base sólida implementada** com domains, use cases e processamento de webhook do WhatsApp. Esta Epic documenta o **estado atual** e define as **melhorias necessárias** para completar o sistema automático de atendimento.

**Pré-requisitos:** Assume que as seguintes Epics já estão implementadas:
- [Epic: Integração com ASAAS - Camada Base](../Services/ASAAS/asaas-main-service.md)
- [Epic: Integração com ASAAS - Organizations](../Services/ASAAS/asaas-organizations-service.md)
- [Epic: Integração com ASAAS - Sales](../Services/ASAAS/asaas-sales-service.md)
- [Epic: Integração com Resend Email](../Core/integracao-servico-email.md)
- [Epic: Sistema de Recuperação de Senha](../Core/recuperacao-senha.md)

## 🔍 Estado Atual do Sistema ChatBot

### ✅ **Funcionalidades Já Implementadas**

| Componente | Status | Localização |
|------------|--------|-------------|
| **Domains Principais** | ✅ Implementado | `app/Domains/ChatBot/` |
| - Flow | ✅ Completo | `Flow.php` - Fluxos de conversação |
| - Step | ✅ Completo | `Step.php` - Passos do fluxo |
| - Component | ✅ Completo | `Component.php` - Componentes de mensagem |
| - Button | ✅ Completo | `Button.php` - Botões interativos |
| - Parameter | ✅ Completo | `Parameter.php` - Parâmetros dinâmicos |
| - Message | ✅ Completo | `Message.php` - Mensagens do sistema |
| - Campaign | ✅ Completo | `Campaign.php` - Campanhas de envio |
| - Template | ✅ Completo | `Template.php` - Templates de mensagem |
| - Conversation | ✅ Completo | `Conversation.php` - Conversas ativas |
| - Interaction | ✅ Completo | `Interaction.php` - Interações do usuário |
| - PhoneNumber | ✅ Completo | `PhoneNumber.php` - Números WhatsApp |
| **Processamento de Webhook** | ✅ Implementado | `app/Services/Meta/WhatsApp/ChatBot/` |
| - ProcessWebhookMessage | ✅ Completo | Processa mensagens recebidas |
| - FindOrCreateClient | ✅ Completo | Gerencia clientes automaticamente |
| - FindOrCreateConversation | ✅ Completo | Gerencia conversas |
| - ProcessFlowStep | ✅ Completo | Processa passos do fluxo |
| - SendWhatsAppResponse | ✅ Completo | Envia respostas automáticas |
| **Navegação Condicional** | ✅ Implementado | Botões com `internal_type='condition'` |
| **Coleta de Input** | ✅ Implementado | Steps com `is_input=true` |
| **Substituição de Variáveis** | ✅ Implementado | Padrão `{{client.name}}` |
| **Integração WhatsApp** | ✅ Implementado | Webhook + API WhatsApp |

### ⚠️ **Funcionalidades Parcialmente Implementadas**

| Componente | Status | Observações |
|------------|--------|-------------|
| **Command Steps** | ⚠️ Parcial | Estrutura existe, lógica não implementada |
| **Conditional Navigation Service** | ⚠️ Parcial | Básico implementado, precisa expansão |
| **Input Validation** | ⚠️ Parcial | Coleta input, mas validação limitada |
| **Error Handling** | ⚠️ Parcial | Tratamento básico, precisa melhorias |
| **Analytics/Metrics** | ⚠️ Parcial | Logs existem, dashboard não implementado |

### ❌ **Funcionalidades Não Implementadas**

| Funcionalidade | Prioridade | Descrição |
|----------------|------------|-----------|
| **Escalação para Humano** | 🔴 Alta | Sistema para transferir para atendente |
| **Agendamento Automático** | 🟡 Média | Integração com calendário/agenda |
| **Análise de Sentimento** | 🟢 Baixa | Detectar satisfação do cliente |
| **Multi-idioma** | 🟢 Baixa | Suporte a múltiplos idiomas |
| **Integração com CRM** | 🟡 Média | Sincronização com sistemas externos |
| **Dashboard de Analytics** | 🟡 Média | Interface para métricas |

## 🎯 Estratégia de Implementação

Devido à **complexidade e extensão** do sistema ChatBot, vou dividir em **múltiplas Epics específicas**:

### **Epic 1: Melhorias do Core ChatBot**
- Completar Command Steps
- Melhorar Error Handling
- Expandir Conditional Navigation
- Validação robusta de Input

### **Epic 2: Sistema de Escalação Humana**
- Transferência para atendentes
- Fila de atendimento
- Notificações para equipe

### **Epic 3: Agendamento Automático**
- Integração com calendário
- Coleta de dados para agendamento
- Confirmações automáticas

### **Epic 4: Analytics e Dashboard**
- Métricas de conversação
- Dashboard administrativo
- Relatórios de performance

### **Epic 5: Integrações Avançadas**
- CRM Integration
- Email notifications via Resend
- ASAAS para cobrança de serviços

## 📊 Arquitetura Atual do Sistema

### **Fluxo de Processamento de Mensagem**
```
1. WhatsApp Webhook → WhatsAppWebhookController
2. ProcessWebhookMessage → Valida e extrai dados
3. FindOrCreateClient → Identifica/cria cliente
4. FindOrCreateConversation → Gerencia conversa ativa
5. ProcessFlowStep → Processa passo atual do fluxo
6. SendWhatsAppResponse → Envia resposta automática
```

### **Estrutura de Domains**
```
Flow (Fluxo principal)
├── Steps (Passos do fluxo)
│   ├── Components (Partes da mensagem)
│   │   ├── Parameters (Variáveis dinâmicas)
│   │   └── Buttons (Botões interativos)
│   └── Conditional Navigation (Navegação baseada em botões)
├── Conversations (Conversas ativas)
│   └── Interactions (Interações do usuário)
└── PhoneNumber (Número WhatsApp associado)
```

### **Tipos de Steps Implementados**
- **`is_message`**: Apenas exibe mensagem e avança
- **`is_interactive`**: Exibe botões/opções para seleção
- **`is_input`**: Coleta input do usuário (nome, email, etc.)
- **`is_command`**: Executa lógica de negócio (parcialmente implementado)

## 🔗 Próximas Epics Detalhadas

As seguintes Epics serão criadas como arquivos separados:

1. **[chatbot-core-improvements.md](./chatbot-core-improvements.md)** - Melhorias do sistema base
2. **[chatbot-human-escalation.md](./chatbot-human-escalation.md)** - Sistema de escalação
3. **[chatbot-scheduling.md](./chatbot-scheduling.md)** - Agendamento automático
4. **[chatbot-analytics.md](./chatbot-analytics.md)** - Analytics e dashboard
5. **[chatbot-integrations.md](./chatbot-integrations.md)** - Integrações avançadas

## 🎯 Conclusão

O sistema ChatBot do Obvio possui uma **base arquitetural sólida** com a maioria dos componentes fundamentais já implementados. O foco agora deve ser em:

1. **Completar funcionalidades parciais** (Command Steps, validações)
2. **Implementar escalação humana** (prioridade alta)
3. **Adicionar analytics** para monitoramento
4. **Integrar com outros sistemas** (ASAAS, Resend, etc.)

## 📚 Referências

- [WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp)
- [Conditional Navigation Guide](../../services/Meta/CONDITIONAL_NAVIGATION_GUIDE.md)
- [ChatBot Domain Architecture](../../app/Domains/ChatBot/)
- [WhatsApp ChatBot Service](../../app/Services/Meta/WhatsApp/ChatBot/)

---

**Próximo Passo:** Implementar [Epic 1: Melhorias do Core ChatBot](./chatbot-core-improvements.md) para completar as funcionalidades básicas.
