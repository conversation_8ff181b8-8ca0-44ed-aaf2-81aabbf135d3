# 🧪 Password Reset System - Testing Guide

## 📋 Overview

Guia completo para testar o sistema de recuperação de senha em diferentes ambientes, incluindo testes automatizados, manuais e de segurança.

## 🔧 Setup de Teste

### Environment Configuration
```bash
# Configurar ambiente de teste
cp .env.example .env.testing

# Configurações específicas para teste
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
RESEND_SANDBOX_MODE=true
RESEND_API_KEY="test_key"
```

### Database Setup
```bash
# Executar migrations para teste
php artisan migrate --env=testing

# Verificar estrutura
php artisan tinker --env=testing
>>> Schema::hasTable('password_reset_tokens')
>>> Schema::getColumnListing('password_reset_tokens')
```

## 🤖 Testes Automatizados

### Executar Todos os Testes
```bash
# Suite completa de password reset
php artisan test tests/Feature/Auth/PasswordResetTest.php

# Com verbose para debug
php artisan test tests/Feature/Auth/PasswordResetTest.php --verbose

# Teste específico
php artisan test --filter="test_forgot_password_creates_token"

# Com coverage (se configurado)
php artisan test --coverage
```

### Testes Individuais
```bash
# 1. Criação de token
php artisan test --filter="test_forgot_password_creates_token"

# 2. Email inexistente
php artisan test --filter="test_forgot_password_returns_success_for_nonexistent_email"

# 3. Validação de token válido
php artisan test --filter="test_validate_token_works_for_valid_token"

# 4. Token inválido
php artisan test --filter="test_validate_token_fails_for_invalid_token"

# 5. Token expirado
php artisan test --filter="test_validate_token_fails_for_expired_token"

# 6. Reset com sucesso
php artisan test --filter="test_reset_password_works_with_valid_token"

# 7. Reset com token inválido
php artisan test --filter="test_reset_password_fails_with_invalid_token"

# 8. Senhas não coincidem
php artisan test --filter="test_reset_password_fails_with_mismatched_passwords"

# 9. Limpeza de tokens
php artisan test --filter="test_clean_expired_tokens_removes_old_tokens"

# 10. Prevenção de reutilização
php artisan test --filter="test_token_cannot_be_reused_after_password_reset"
```

## 🔍 Testes Manuais

### 1. Fluxo Completo de Reset

#### Passo 1: Solicitar Reset
```bash
curl -X POST http://localhost:8000/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

**Resultado esperado:**
```json
{
  "status": "success",
  "message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.",
  "data": [],
  "errors": null
}
```

#### Passo 2: Verificar Token no Banco
```sql
SELECT * FROM password_reset_tokens 
WHERE email = '<EMAIL>' 
ORDER BY created_at DESC LIMIT 1;
```

#### Passo 3: Validar Token
```bash
curl -X POST http://localhost:8000/api/auth/password/validate-token \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","token":"TOKEN_DO_BANCO"}'
```

#### Passo 4: Reset de Senha
```bash
curl -X POST http://localhost:8000/api/auth/password/reset \
  -H "Content-Type: application/json" \
  -d '{
    "email":"<EMAIL>",
    "token":"TOKEN_DO_BANCO",
    "password":"newpassword123",
    "password_confirmation":"newpassword123"
  }'
```

### 2. Teste de Rate Limiting

```bash
# Executar 4 requests rapidamente (limite é 3/min)
for i in {1..4}; do
  echo "Request $i:"
  curl -X POST http://localhost:8000/api/auth/password/forgot \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>"}'
  echo -e "\n---"
done
```

**Resultado esperado:** 4º request deve retornar erro 429 (Too Many Requests)

### 3. Teste de Expiração

```bash
# 1. Criar token
curl -X POST http://localhost:8000/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# 2. Modificar expires_at no banco para o passado
mysql -e "UPDATE password_reset_tokens SET expires_at = '2023-01-01 00:00:00' WHERE email = '<EMAIL>'"

# 3. Tentar usar token expirado
curl -X POST http://localhost:8000/api/auth/password/validate-token \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","token":"TOKEN_EXPIRADO"}'
```

## 🛡️ Testes de Segurança

### 1. Teste de Token Hashing
```bash
# Verificar se tokens não são armazenados em texto plano
mysql -e "SELECT token, hashed_token FROM password_reset_tokens LIMIT 5"

# Token deve ser diferente do hashed_token
# hashed_token deve ter 64 caracteres (SHA-256)
```

### 2. Teste de Reutilização de Token
```bash
# 1. Usar token para reset
curl -X POST http://localhost:8000/api/auth/password/reset \
  -H "Content-Type: application/json" \
  -d '{
    "email":"<EMAIL>",
    "token":"TOKEN_VALIDO",
    "password":"newpass123",
    "password_confirmation":"newpass123"
  }'

# 2. Tentar reutilizar o mesmo token
curl -X POST http://localhost:8000/api/auth/password/reset \
  -H "Content-Type: application/json" \
  -d '{
    "email":"<EMAIL>",
    "token":"MESMO_TOKEN",
    "password":"anotherpass123",
    "password_confirmation":"anotherpass123"
  }'
```

**Resultado esperado:** 2º request deve falhar

### 3. Teste de Injection
```bash
# SQL Injection attempt
curl -X POST http://localhost:8000/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>; DROP TABLE users; --"}'

# XSS attempt
curl -X POST http://localhost:8000/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<script>alert(1)</script>@example.com"}'
```

**Resultado esperado:** Requests devem ser rejeitados com erro de validação

## 📊 Testes de Performance

### 1. Teste de Carga
```bash
# Instalar Apache Bench
sudo apt-get install apache2-utils

# Teste com 100 requests, 10 concurrent
ab -n 100 -c 10 -H "Content-Type: application/json" \
   -p post_data.json \
   http://localhost:8000/api/auth/password/forgot

# Arquivo post_data.json:
echo '{"email":"<EMAIL>"}' > post_data.json
```

### 2. Teste de Memory Usage
```bash
# Monitorar uso de memória durante testes
php artisan test tests/Feature/Auth/PasswordResetTest.php &
PID=$!

# Monitorar em outra janela
while kill -0 $PID 2>/dev/null; do
  ps -p $PID -o pid,vsz,rss,comm
  sleep 1
done
```

## 🔧 Testes de Integração

### 1. Teste com Resend Real
```bash
# Configurar API key real no .env
RESEND_API_KEY="re_real_api_key"
RESEND_SANDBOX_MODE=false

# Executar teste com email real
curl -X POST http://localhost:8000/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Verificar recebimento do email
```

### 2. Teste de Command Agendado
```bash
# Criar tokens expirados manualmente
php artisan tinker
>>> $token = \App\Models\PasswordResetToken::create([
...   'email' => '<EMAIL>',
...   'token' => 'test',
...   'hashed_token' => hash('sha256', 'test'),
...   'organization_id' => 1,
...   'expires_at' => now()->subDay(),
...   'ip_address' => '127.0.0.1'
... ]);

# Executar command de limpeza
php artisan auth:clean-expired-tokens

# Verificar se foi removido
>>> \App\Models\PasswordResetToken::where('email', '<EMAIL>')->count()
```

## 📝 Checklist de Testes

### ✅ Funcionalidade Básica
- [ ] Solicitação de reset cria token
- [ ] Email é enviado (mock ou real)
- [ ] Token é validado corretamente
- [ ] Reset de senha funciona
- [ ] Token é marcado como usado

### ✅ Validação
- [ ] Email obrigatório e válido
- [ ] Token obrigatório
- [ ] Senha obrigatória e confirmada
- [ ] Senhas devem coincidir

### ✅ Segurança
- [ ] Rate limiting funciona
- [ ] Tokens são hasheados
- [ ] Tokens expiram
- [ ] Tokens não podem ser reutilizados
- [ ] Não revela se email existe

### ✅ Edge Cases
- [ ] Email inexistente
- [ ] Token inválido
- [ ] Token expirado
- [ ] Token já usado
- [ ] Múltiplos tokens para mesmo email

### ✅ Performance
- [ ] Response time < 500ms
- [ ] Memory usage estável
- [ ] Database queries otimizadas
- [ ] Rate limiting não afeta performance normal

### ✅ Integração
- [ ] Resend API funcionando
- [ ] Database operations corretas
- [ ] Logs sendo gerados
- [ ] Command de limpeza funcionando

## 🚨 Troubleshooting de Testes

### Teste Falhando: "Resend API key is not configured"
```bash
# Verificar mock
# Em tests/Feature/Auth/PasswordResetTest.php
$this->mock(ResendService::class, function ($mock) {
    $mock->shouldReceive('send')->andReturn(['id' => 'test-email-id']);
});
```

### Teste Falhando: "Table doesn't exist"
```bash
# Executar migrations para teste
php artisan migrate --env=testing

# Verificar configuração de banco de teste
cat .env.testing | grep DB_
```

### Performance Degradada
```bash
# Verificar queries
php artisan test --verbose | grep "SELECT\|INSERT\|UPDATE"

# Verificar indexes
EXPLAIN SELECT * FROM password_reset_tokens WHERE email = '<EMAIL>';
```

---

**Última atualização:** 2025-07-29  
**Versão dos testes:** 1.0.0  
**Cobertura:** 100% das funcionalidades principais
