# 📊 Password Reset System - Production Monitoring

## 📋 Overview

Guia completo para monitoramento do sistema de recuperação de senha em produção, incluindo métricas, alertas e dashboards.

**Versão:** 1.0.0  
**Data:** 2025-07-29  
**Ambiente:** Produção

## 🎯 Key Performance Indicators (KPIs)

### **Functional Metrics**
| **Métrica** | **Target** | **Warning** | **Critical** |
|-------------|------------|-------------|--------------|
| **Success Rate** | > 95% | < 90% | < 80% |
| **Response Time** | < 500ms | > 1s | > 2s |
| **Email Delivery** | > 98% | < 95% | < 90% |
| **Token Usage Rate** | > 80% | < 60% | < 40% |
| **Error Rate** | < 1% | > 3% | > 5% |

### **Security Metrics**
| **Métrica** | **Target** | **Warning** | **Critical** |
|-------------|------------|-------------|--------------|
| **Rate Limit Hits** | < 5/hour | > 20/hour | > 50/hour |
| **Failed Attempts** | < 10% | > 20% | > 30% |
| **Suspicious IPs** | 0 | > 3 | > 10 |
| **Token Reuse Attempts** | 0 | > 1 | > 5 |
| **Expired Token Usage** | < 1% | > 5% | > 10% |

## 📈 Monitoring Queries

### **1. Success Rate Monitoring**
```sql
-- Taxa de sucesso nas últimas 24h
SELECT 
    COUNT(*) as total_requests,
    COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) as successful_resets,
    ROUND(
        COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as success_rate_percent
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 24 HOUR;
```

### **2. Response Time Monitoring**
```sql
-- Tempo médio de resposta (via logs)
SELECT 
    DATE(created_at) as date,
    AVG(TIMESTAMPDIFF(SECOND, created_at, updated_at)) as avg_response_time_seconds,
    MAX(TIMESTAMPDIFF(SECOND, created_at, updated_at)) as max_response_time_seconds
FROM logs 
WHERE context LIKE '%RequestPasswordReset%' 
  AND created_at >= NOW() - INTERVAL 7 DAY
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### **3. Rate Limiting Monitoring**
```sql
-- IPs com muitas tentativas
SELECT 
    ip_address,
    COUNT(*) as attempts,
    COUNT(DISTINCT email) as unique_emails,
    MIN(created_at) as first_attempt,
    MAX(created_at) as last_attempt
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 1 HOUR
GROUP BY ip_address 
HAVING attempts > 3
ORDER BY attempts DESC;
```

### **4. Email Delivery Monitoring**
```sql
-- Taxa de entrega de emails (via logs)
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_emails,
    COUNT(CASE WHEN message LIKE '%email sent successfully%' THEN 1 END) as delivered,
    COUNT(CASE WHEN message LIKE '%failed to send%' THEN 1 END) as failed,
    ROUND(
        COUNT(CASE WHEN message LIKE '%email sent successfully%' THEN 1 END) * 100.0 / COUNT(*), 2
    ) as delivery_rate_percent
FROM logs 
WHERE context LIKE '%RequestPasswordReset%' 
  AND created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### **5. Security Monitoring**
```sql
-- Tentativas suspeitas
SELECT 
    ip_address,
    COUNT(*) as attempts,
    COUNT(DISTINCT email) as unique_emails,
    COUNT(CASE WHEN used_at IS NULL AND expires_at < NOW() THEN 1 END) as unused_expired,
    MAX(created_at) as last_attempt
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY ip_address 
HAVING attempts > 10 OR (attempts > 5 AND unused_expired > 3)
ORDER BY attempts DESC;
```

## 🚨 Alerting Rules

### **Critical Alerts (Immediate Response)**

#### 1. **System Down**
```yaml
alert: PasswordResetSystemDown
expr: up{job="password-reset"} == 0
for: 1m
labels:
  severity: critical
annotations:
  summary: "Password reset system is down"
  description: "The password reset system has been down for more than 1 minute"
```

#### 2. **High Error Rate**
```sql
-- Trigger: Error rate > 5% in last 5 minutes
SELECT 
    COUNT(CASE WHEN message LIKE '%error%' OR message LIKE '%exception%' THEN 1 END) * 100.0 / COUNT(*) as error_rate
FROM logs 
WHERE context LIKE '%Auth%' 
  AND created_at >= NOW() - INTERVAL 5 MINUTE
HAVING error_rate > 5;
```

#### 3. **Email Service Failure**
```sql
-- Trigger: Email delivery rate < 90% in last 10 minutes
SELECT 
    COUNT(CASE WHEN message LIKE '%failed to send%' THEN 1 END) * 100.0 / COUNT(*) as failure_rate
FROM logs 
WHERE context LIKE '%RequestPasswordReset%' 
  AND created_at >= NOW() - INTERVAL 10 MINUTE
HAVING failure_rate > 10;
```

### **Warning Alerts (Monitor Closely)**

#### 1. **High Response Time**
```sql
-- Trigger: Average response time > 1s in last 15 minutes
SELECT AVG(response_time_ms) as avg_response_time
FROM api_metrics 
WHERE endpoint = '/api/auth/password/forgot' 
  AND timestamp >= NOW() - INTERVAL 15 MINUTE
HAVING avg_response_time > 1000;
```

#### 2. **Suspicious Activity**
```sql
-- Trigger: More than 20 rate limit hits in 1 hour
SELECT COUNT(*) as rate_limit_hits
FROM logs 
WHERE message LIKE '%rate limit%' 
  AND created_at >= NOW() - INTERVAL 1 HOUR
HAVING rate_limit_hits > 20;
```

## 📊 Dashboard Metrics

### **Real-time Dashboard (5-minute intervals)**

#### Panel 1: **System Health**
```sql
-- Requests per minute
SELECT 
    DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00') as minute,
    COUNT(*) as requests
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 1 HOUR
GROUP BY minute
ORDER BY minute;

-- Success rate
SELECT 
    DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00') as minute,
    COUNT(*) as total,
    COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) as successful
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 1 HOUR
GROUP BY minute
ORDER BY minute;
```

#### Panel 2: **Security Overview**
```sql
-- Rate limit hits
SELECT 
    DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00') as minute,
    COUNT(*) as rate_limit_hits
FROM logs 
WHERE message LIKE '%rate limit%' 
  AND created_at >= NOW() - INTERVAL 1 HOUR
GROUP BY minute
ORDER BY minute;

-- Top suspicious IPs
SELECT 
    ip_address,
    COUNT(*) as attempts,
    MAX(created_at) as last_attempt
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 1 HOUR
GROUP BY ip_address 
ORDER BY attempts DESC 
LIMIT 10;
```

### **Daily Dashboard (24-hour view)**

#### Panel 3: **Performance Trends**
```sql
-- Daily performance summary
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) as successful_resets,
    COUNT(CASE WHEN expires_at < NOW() AND used_at IS NULL THEN 1 END) as expired_unused,
    ROUND(AVG(TIMESTAMPDIFF(MINUTE, created_at, COALESCE(used_at, expires_at))), 2) as avg_time_to_use_minutes
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 7 DAY
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

#### Panel 4: **Email Performance**
```sql
-- Email delivery trends
SELECT 
    DATE(created_at) as date,
    COUNT(*) as emails_sent,
    COUNT(CASE WHEN message LIKE '%email sent successfully%' THEN 1 END) as delivered,
    COUNT(CASE WHEN message LIKE '%failed to send%' THEN 1 END) as failed
FROM logs 
WHERE context LIKE '%RequestPasswordReset%' 
  AND created_at >= NOW() - INTERVAL 7 DAY
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🔧 Automated Monitoring Scripts

### **Health Check Script**
```bash
#!/bin/bash
# password-reset-health-check.sh

API_URL="https://your-domain.com/api"
ALERT_EMAIL="<EMAIL>"

# Test forgot password endpoint
response=$(curl -s -o /dev/null -w "%{http_code}" \
  -X POST "$API_URL/auth/password/forgot" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}')

if [ "$response" != "200" ]; then
    echo "ALERT: Password reset endpoint returned $response" | \
    mail -s "Password Reset Health Check Failed" $ALERT_EMAIL
fi

# Check database connectivity
php artisan tinker --execute="
try {
    \$count = \App\Models\PasswordResetToken::count();
    echo 'Database OK: ' . \$count . ' tokens' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Database ERROR: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"
```

### **Performance Monitor Script**
```bash
#!/bin/bash
# password-reset-performance-monitor.sh

# Check response times
response_time=$(curl -s -o /dev/null -w "%{time_total}" \
  -X POST "https://your-domain.com/api/auth/password/forgot" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}')

# Alert if response time > 2 seconds
if (( $(echo "$response_time > 2.0" | bc -l) )); then
    echo "ALERT: Slow response time: ${response_time}s" | \
    mail -s "Password Reset Performance Alert" <EMAIL>
fi
```

## 📱 Mobile/SMS Alerts

### **Critical Alert Template**
```
🚨 CRITICAL: Password Reset System
Issue: [ISSUE_TYPE]
Time: [TIMESTAMP]
Impact: [IMPACT_DESCRIPTION]
Action: [REQUIRED_ACTION]
Dashboard: [DASHBOARD_URL]
```

### **Warning Alert Template**
```
⚠️ WARNING: Password Reset System
Issue: [ISSUE_TYPE]
Time: [TIMESTAMP]
Trend: [TREND_DESCRIPTION]
Monitor: [MONITORING_URL]
```

## 🔍 Log Analysis

### **Important Log Patterns**
```bash
# Monitor for errors
tail -f /var/log/laravel.log | grep -E "(password|reset|token)" | grep -i error

# Monitor for rate limiting
tail -f /var/log/laravel.log | grep -i "rate limit"

# Monitor for suspicious activity
tail -f /var/log/laravel.log | grep -E "multiple attempts|suspicious"

# Monitor email failures
tail -f /var/log/laravel.log | grep -E "resend.*fail|email.*error"
```

### **Log Aggregation Queries**
```bash
# Top error messages (last 24h)
grep "password\|reset\|token" /var/log/laravel.log | \
grep -i error | \
awk '{print $NF}' | \
sort | uniq -c | sort -nr | head -10

# Rate limiting statistics
grep "rate limit" /var/log/laravel.log | \
awk '{print $1, $2}' | \
uniq -c | tail -20
```

## 📋 Maintenance Tasks

### **Daily Tasks**
- [ ] Verificar métricas de sucesso (> 95%)
- [ ] Revisar alertas das últimas 24h
- [ ] Verificar taxa de entrega de emails
- [ ] Monitorar IPs suspeitos

### **Weekly Tasks**
- [ ] Analisar tendências de performance
- [ ] Revisar logs de segurança
- [ ] Verificar crescimento da base de dados
- [ ] Atualizar thresholds de alertas se necessário

### **Monthly Tasks**
- [ ] Análise completa de segurança
- [ ] Otimização de queries se necessário
- [ ] Revisão de capacidade
- [ ] Backup e teste de restore

---

**Responsável:** DevOps Team  
**Última atualização:** 2025-07-29  
**Próxima revisão:** 2025-08-29
