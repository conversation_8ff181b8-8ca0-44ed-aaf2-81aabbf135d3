# 🔧 Troubleshooting Guide - Resend Email Integration

## 🚨 Problemas Comuns e Soluções

### **1. "Resend API key is not configured"**

**Causa**: API key não configurada ou vazia

**Solução**:
```bash
# Verificar se variável existe
echo $RESEND_API_KEY

# Adicionar no .env
RESEND_API_KEY="re_xxxxxxxxx_your_api_key_here"

# Limpar cache de configuração
php artisan config:clear
```

---

### **2. "Resend authentication failed"**

**Causa**: API key inválida ou expirada

**Soluções**:
1. **Verificar API key no dashboard Resend**
2. **Gerar nova API key se necessário**
3. **Verificar se não há espaços extras na configuração**

```bash
# Testar API key manualmente
curl -X POST 'https://api.resend.com/emails' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Test",
    "html": "<p>Test email</p>"
  }'
```

---

### **3. Emails não chegam na caixa de entrada**

**Possíveis causas**:
- Domínio não verificado no Resend
- Configuração DNS incorreta
- Emails indo para spam

**Soluções**:

1. **Verificar domínio no Resend**:
   - Acesse dashboard do Resend
   - Vá em "Domains"
   - Verifique se domínio está verificado

2. **Configurar DNS records**:
   ```
   # SPF Record
   TXT @ "v=spf1 include:_spf.resend.com ~all"
   
   # DKIM Record (fornecido pelo Resend)
   TXT resend._domainkey "p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC..."
   ```

3. **Verificar spam**:
   - Checar pasta de spam/lixo eletrônico
   - Adicionar remetente à lista de contatos confiáveis

---

### **4. "Email template does not exist"**

**Causa**: Template Blade não encontrado

**Solução**:
```bash
# Verificar se template existe
ls -la resources/views/emails/

# Verificar sintaxe do template
php artisan view:clear
```

---

### **5. Erro de timeout na API**

**Causa**: Rede lenta ou problemas no Resend

**Soluções**:
1. **Aumentar timeout**:
   ```env
   RESEND_TIMEOUT=60
   ```

2. **Verificar status do Resend**:
   - Acesse https://status.resend.com

3. **Implementar retry manual**:
   ```php
   try {
       $result = $sendUseCase->perform($email);
   } catch (ResendException $e) {
       // Log erro e tentar novamente depois
       Log::error('Email failed, will retry', ['error' => $e->getMessage()]);
   }
   ```

---

### **6. Rate limit exceeded**

**Causa**: Muitos emails enviados rapidamente

**Soluções**:
1. **Verificar limites da conta Resend**
2. **Implementar queue para emails**:
   ```php
   // Usar jobs para envio assíncrono
   dispatch(new SendEmailJob($email));
   ```

3. **Espaçar envios**:
   ```php
   // Adicionar delay entre envios
   sleep(1); // 1 segundo entre emails
   ```

---

## 🔍 Comandos de Diagnóstico

### **Verificar Configuração**
```bash
# Mostrar configuração atual
php artisan config:show resend

# Testar conexão com banco
php artisan migrate:status

# Verificar logs recentes
tail -n 50 storage/logs/laravel.log | grep -i resend
```

### **Testar Templates**
```bash
# Verificar se templates compilam
php artisan view:cache
php artisan view:clear
```

### **Verificar Logs no Banco**
```sql
-- Logs de email dos últimos 30 minutos
SELECT * FROM logs 
WHERE `from` LIKE '%Resend%' 
AND created_at > NOW() - INTERVAL 30 MINUTE
ORDER BY created_at DESC;

-- Contar emails por status
SELECT 
    is_error,
    COUNT(*) as total
FROM logs 
WHERE `from` LIKE '%Resend%' 
AND created_at > CURDATE()
GROUP BY is_error;
```

---

## 🧪 Modo Debug

### **Ativar Logs Detalhados**
```env
LOG_LEVEL=debug
RESEND_SANDBOX_MODE=true
```

### **Testar Manualmente**
```php
// No tinker: php artisan tinker
use App\Services\Resend\UseCases\Send;
use App\Services\Resend\Domains\WelcomeEmail;
use App\Domains\User;
use App\Domains\Organization;

$user = User::find(1);
$org = Organization::find(1);
$email = new WelcomeEmail($user, $org);
$sendUseCase = app()->make(Send::class);

try {
    $result = $sendUseCase->perform($email);
    dump($result);
} catch (Exception $e) {
    dump($e->getMessage());
}
```

---

## 📊 Monitoramento

### **Métricas Importantes**
- Taxa de entrega (delivery rate)
- Taxa de bounce
- Tempo de resposta da API
- Número de erros por hora

### **Alertas Recomendados**
```bash
# Criar alerta para muitos erros
# Se > 10 erros de email em 1 hora, enviar notificação

# Monitorar uso da API
# Se próximo do limite, alertar equipe
```

---

## 🆘 Contatos de Emergência

### **Suporte Resend**
- **Email**: <EMAIL>
- **Documentação**: https://resend.com/docs
- **Status**: https://status.resend.com

### **Escalação Interna**
1. **Verificar logs e dashboard**
2. **Ativar modo sandbox temporariamente**
3. **Contactar equipe de DevOps**
4. **Se crítico, usar sistema de email backup**

---

## 🔄 Procedimento de Rollback

Se problemas persistirem:

1. **Ativar sandbox mode**:
   ```env
   RESEND_SANDBOX_MODE=true
   ```

2. **Desabilitar funcionalidade**:
   ```php
   // Comentar código que envia emails
   // return; // Desabilitar temporariamente
   ```

3. **Usar sistema anterior** (se existir):
   ```env
   MAIL_MAILER=smtp
   # Configurações do sistema anterior
   ```

4. **Notificar stakeholders**
5. **Investigar problema com calma**
