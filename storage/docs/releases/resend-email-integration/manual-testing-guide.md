# 🧪 Manual Testing Guide - Resend Email Integration

## 📋 Testes Obrigatórios em Produção

### **1. Teste de Email de Redefinição de Senha**

**Objetivo**: Verificar se emails de reset de senha são enviados corretamente

**Passos**:
1. Acesse a página de login
2. Clique em "Esqueci minha senha"
3. Digite um email válido de usuário existente
4. Clique em "Enviar"
5. Verifique se o email chegou na caixa de entrada
6. Clique no link do email
7. Redefina a senha
8. Faça login com a nova senha

**Critérios de Sucesso**:
- [ ] Email recebido em menos de 2 minutos
- [ ] Template renderizado corretamente
- [ ] Link funciona e não expirou
- [ ] Dados do usuário aparecem corretamente
- [ ] Email não foi para spam

---

### **2. Teste de Email de Boas-vindas**

**Objetivo**: Verificar se novos usuários recebem email de boas-vindas

**Passos**:
1. Crie um novo usuário via admin ou registro
2. Verifique se o email de boas-vindas foi enviado
3. Abra o email recebido
4. Clique nos links do email
5. Verifique se redirecionam corretamente

**Critérios de Sucesso**:
- [ ] Email enviado automaticamente após criação
- [ ] Nome do usuário e organização corretos
- [ ] Links funcionam corretamente
- [ ] Template profissional e responsivo

---

### **3. Teste de Email de Relatório de Campanha**

**Objetivo**: Verificar se relatórios são enviados corretamente

**Passos**:
1. Execute uma campanha com dados de teste
2. Gere um relatório da campanha
3. Envie o relatório por email
4. Verifique o email recebido
5. Confirme se os dados estão corretos

**Critérios de Sucesso**:
- [ ] Métricas aparecem corretamente
- [ ] Tabelas formatadas adequadamente
- [ ] Período da campanha correto
- [ ] Links para campanha funcionam

---

### **4. Teste de Email de Fatura**

**Objetivo**: Verificar se faturas são enviadas corretamente

**Passos**:
1. Crie uma fatura de teste
2. Envie a fatura por email
3. Verifique o email recebido
4. Teste o link de pagamento
5. Confirme formatação dos valores

**Critérios de Sucesso**:
- [ ] Valores formatados em R$ corretamente
- [ ] Itens da fatura listados
- [ ] Link de pagamento funciona
- [ ] Data de vencimento correta

---

## 🔍 Testes de Configuração

### **Teste de Sandbox Mode**

**Quando usar**: Durante desenvolvimento ou testes

**Configuração**:
```env
RESEND_SANDBOX_MODE=true
```

**Resultado esperado**: Todos os emails vão para `<EMAIL>`

### **Teste de Produção**

**Configuração**:
```env
RESEND_SANDBOX_MODE=false
```

**Resultado esperado**: Emails vão para destinatários reais

---

## 📊 Verificação de Logs

### **Logs da Aplicação**
```bash
# Verificar logs de sucesso
tail -f storage/logs/laravel.log | grep "Email sent successfully"

# Verificar logs de erro
tail -f storage/logs/laravel.log | grep "Email sending failed"
```

### **Logs no Banco de Dados**
```sql
-- Verificar logs de email no banco
SELECT * FROM logs 
WHERE `from` LIKE '%Resend%' 
ORDER BY created_at DESC 
LIMIT 10;
```

### **Dashboard do Resend**
1. Acesse [resend.com/dashboard](https://resend.com/dashboard)
2. Verifique a seção "Emails"
3. Confirme status de entrega
4. Verifique métricas de abertura (se configurado)

---

## 🚨 Cenários de Erro para Testar

### **1. API Key Inválida**
- Temporariamente use uma API key inválida
- Verifique se erro é logado corretamente
- Confirme que aplicação não quebra

### **2. Email Inválido**
- Tente enviar para email com formato inválido
- Verifique se validação funciona
- Confirme que erro é tratado

### **3. Template Inexistente**
- Modifique código para usar template que não existe
- Verifique se erro é capturado
- Confirme que aplicação continua funcionando

### **4. Rate Limit**
- Envie muitos emails rapidamente
- Verifique se retry funciona
- Confirme que erros são logados

---

## ✅ Checklist Final

Após todos os testes:

- [ ] Todos os tipos de email funcionam
- [ ] Templates renderizam corretamente
- [ ] Logs são criados adequadamente
- [ ] Emails não vão para spam
- [ ] Performance é aceitável
- [ ] Erros são tratados graciosamente
- [ ] Dashboard do Resend mostra entregas
- [ ] Configurações de produção estão corretas

---

## 📞 Em Caso de Problemas

1. **Verifique logs da aplicação**
2. **Consulte dashboard do Resend**
3. **Teste com RESEND_SANDBOX_MODE=true**
4. **Verifique configurações DNS do domínio**
5. **Consulte [troubleshooting-guide.md](./troubleshooting-guide.md)**
