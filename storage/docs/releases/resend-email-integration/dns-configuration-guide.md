# 🌐 DNS Configuration Guide - Resend Email Integration

## 📋 Configuração Obrigatória de DNS

Para garantir alta taxa de entrega e evitar que emails vão para spam, é essencial configurar corretamente os registros DNS.

### **1. Verificação de Domínio no Resend**

**Passos**:
1. Acesse [resend.com/dashboard](https://resend.com/dashboard)
2. Vá para "Domains"
3. Clique em "Add Domain"
4. Digite seu domínio (ex: `yourdomain.com`)
5. <PERSON><PERSON> as instruções para verificação

---

### **2. Registros DNS Obrigatórios**

#### **SPF Record (Sender Policy Framework)**
```
Tipo: TXT
Nome: @ (ou deixe vazio)
Valor: v=spf1 include:_spf.resend.com ~all
TTL: 3600
```

**Função**: Autoriza o Resend a enviar emails em nome do seu domínio.

#### **DKIM Record (DomainKeys Identified Mail)**
```
Tipo: TXT
Nome: resend._domainkey
Valor: [Fornecido pelo Resend no dashboard]
TTL: 3600
```

**Função**: Assina digitalmente os emails para verificar autenticidade.

#### **DMARC Record (Domain-based Message Authentication)**
```
Tipo: TXT
Nome: _dmarc
Valor: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>
TTL: 3600
```

**Função**: Define política para emails que falham na verificação SPF/DKIM.

---

### **3. Configuração por Provedor**

#### **Cloudflare**
1. Acesse o dashboard do Cloudflare
2. Selecione seu domínio
3. Vá para "DNS" > "Records"
4. Clique "Add record"
5. Configure cada registro conforme especificado

#### **GoDaddy**
1. Acesse "My Products" > "DNS"
2. Clique em "Manage" ao lado do domínio
3. Vá para "DNS Records"
4. Adicione os registros TXT

#### **Route 53 (AWS)**
1. Acesse o console do Route 53
2. Selecione a hosted zone
3. Clique "Create Record"
4. Configure cada registro

#### **Namecheap**
1. Acesse "Domain List"
2. Clique "Manage" ao lado do domínio
3. Vá para "Advanced DNS"
4. Adicione os registros TXT

---

### **4. Verificação da Configuração**

#### **Verificar SPF**
```bash
# Linux/Mac
dig TXT yourdomain.com | grep spf

# Windows
nslookup -type=TXT yourdomain.com
```

**Resultado esperado**:
```
yourdomain.com. 3600 IN TXT "v=spf1 include:_spf.resend.com ~all"
```

#### **Verificar DKIM**
```bash
dig TXT resend._domainkey.yourdomain.com
```

#### **Verificar DMARC**
```bash
dig TXT _dmarc.yourdomain.com
```

#### **Ferramentas Online**
- [MXToolbox](https://mxtoolbox.com/spf.aspx)
- [DKIM Validator](https://dkimvalidator.com/)
- [DMARC Analyzer](https://www.dmarcanalyzer.com/)

---

### **5. Configurações Avançadas**

#### **Subdomain Setup**
Se usar subdomínio para emails (ex: `mail.yourdomain.com`):

```
# SPF para subdomínio
Tipo: TXT
Nome: mail
Valor: v=spf1 include:_spf.resend.com ~all

# DKIM para subdomínio
Tipo: TXT
Nome: resend._domainkey.mail
Valor: [Valor fornecido pelo Resend]
```

#### **Multiple Email Providers**
Se usar múltiplos provedores de email:

```
# SPF com múltiplos includes
v=spf1 include:_spf.resend.com include:_spf.google.com ~all
```

---

### **6. Tempo de Propagação**

**Tempo típico**: 24-48 horas
**Verificação**: Use ferramentas online para confirmar propagação

**Durante a propagação**:
- Alguns emails podem ir para spam
- Taxa de entrega pode ser menor
- Monitore logs do Resend

---

### **7. Troubleshooting DNS**

#### **Problema: SPF não encontrado**
```bash
# Verificar se registro existe
dig TXT yourdomain.com

# Verificar sintaxe
# Deve começar com "v=spf1"
# Deve terminar com "~all" ou "-all"
```

#### **Problema: DKIM inválido**
- Verificar se copiou valor completo do Resend
- Verificar se não há espaços extras
- Confirmar nome do registro: `resend._domainkey`

#### **Problema: Emails ainda vão para spam**
1. Aguardar propagação completa (48h)
2. Verificar reputação do domínio
3. Configurar DMARC policy
4. Aquecer o domínio gradualmente

---

### **8. Monitoramento Contínuo**

#### **Alertas Recomendados**
- Monitor de DNS para detectar mudanças
- Alertas de taxa de bounce alta
- Monitoramento de reputação do domínio

#### **Ferramentas de Monitoramento**
- [Postmark DNS Checker](https://dnscheck.postmarkapp.com/)
- [Mail Tester](https://www.mail-tester.com/)
- [Google Postmaster Tools](https://postmaster.google.com/)

---

### **9. Checklist Final**

Antes de ir para produção:

- [ ] SPF record configurado e verificado
- [ ] DKIM record configurado e verificado
- [ ] DMARC record configurado (opcional mas recomendado)
- [ ] Domínio verificado no dashboard Resend
- [ ] Propagação DNS completa (48h)
- [ ] Teste de entrega realizado
- [ ] Emails não vão para spam
- [ ] Monitoramento configurado

---

### **10. Exemplo Completo**

Para domínio `obvio.com`:

```
# SPF
obvio.com. 3600 IN TXT "v=spf1 include:_spf.resend.com ~all"

# DKIM
resend._domainkey.obvio.com. 3600 IN TXT "p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC..."

# DMARC
_dmarc.obvio.com. 3600 IN TXT "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

**Configuração no .env**:
```env
RESEND_FROM_EMAIL="<EMAIL>"
RESEND_FROM_NAME="Obvio"
RESEND_SUPPORT_EMAIL="<EMAIL>"
```
