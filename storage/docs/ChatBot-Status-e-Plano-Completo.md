# ChatBot WhatsApp - Status Atual e Plano para 100% Funcional

## 📋 Status Atual do Sistema

### ✅ Funcionalidades Implementadas

#### 1. **Estrutura de Domains**
- **Flow**: Fluxos de conversa com sequência de passos
- **Step**: Passos individuais com lógica condicional
- **Conversation**: Sessões de chat ativas com clientes
- **Interaction**: Rastreamento de interações do usuário
- **PhoneNumber**: Gerenciamento de números WhatsApp Business
- **Message**: Mensagens individuais com tracking de entrega

#### 2. **Processamento de Webhook**
```
WhatsApp Webhook → WhatsAppWebhookController
├── ProcessWebhookMessage → Valida e extrai dados
├── FindOrCreateClient → Identifica/cria cliente
├── FindOrCreateConversation → Gerencia conversa ativa
├── ProcessFlowStep → Processa passo atual do fluxo
└── SendWhatsAppResponse → Envia resposta automática
```

#### 3. **Tipos de Steps Suportados**
- **Message Steps**: Envio de mensagens simples
- **Interactive Steps**: Botões e listas interativas
- **Input Steps**: Coleta de dados do usuário
- **Command Steps**: Execução de comandos específicos

#### 4. **Navegação Condicional**
- Navegação baseada em botões com `internal_type: 'condition'`
- Identificação de steps por atributo `step`
- Coleta de input com padrão `client.field`

#### 5. **Timeout e Cleanup Básico**
- `ConversationTimeoutService` implementado
- Command `chatbot:cleanup-conversations` disponível
- Configuração em `config/chatbot.php`

### ⚠️ Limitações Atuais

#### 1. **Timeout por Flow Não Configurável**
- Timeout global fixo (24h por padrão)
- Não há campos `inactivity_minutes` e `ending_conversation_message` no Flow
- Cleanup não considera configurações específicas por flow

#### 2. **Cron Job Não Agendado**
- Command existe mas não está no `app/Console/Kernel.php`
- Não há execução automática do cleanup

#### 3. **Finalização de Conversa Limitada**
- Apenas marca `is_finished = true`
- Não envia mensagem de encerramento personalizada
- Não considera timeout específico por flow

#### 4. **Testes de Integração Ausentes**
- Não há testes end-to-end de fluxos completos
- Falta cobertura de cenários de timeout

## 🎯 Plano para 100% Funcional

### 1. **Extensão do Domain Flow**

#### 1.1 Adicionar Campos de Timeout
```php
// app/Domains/ChatBot/Flow.php
public ?int $inactivity_minutes;
public ?string $ending_conversation_message;
```

#### 1.2 Atualizar Migrations
```sql
ALTER TABLE flows ADD COLUMN inactivity_minutes INT DEFAULT 60;
ALTER TABLE flows ADD COLUMN ending_conversation_message TEXT;
```

#### 1.3 Atualizar Factory e Repository
- Incluir novos campos em `FlowFactory`
- Atualizar métodos `toArray()`, `toStoreArray()`, `toUpdateArray()`

### 2. **Melhorar ConversationTimeoutService**

#### 2.1 Timeout Específico por Flow
```php
public function findTimedOutConversationsByFlow(): array
{
    // Buscar conversas com timeout baseado no flow->inactivity_minutes
    // Considerar última interação + inactivity_minutes do flow
}
```

#### 2.2 Envio de Mensagem de Encerramento
```php
public function handleTimeoutWithMessage(WhatsAppConversation $conversation): array
{
    // 1. Enviar flow->ending_conversation_message
    // 2. Marcar conversa como finished
    // 3. Registrar timeout no log
}
```

### 3. **Agendamento de Cron Job**

#### 3.1 Adicionar ao Kernel
```php
// app/Console/Kernel.php
$schedule->command('chatbot:cleanup-conversations')->everyFiveMinutes();
```

#### 3.2 Melhorar Command
- Processar apenas conversas com timeout específico por flow
- Enviar mensagens de encerramento antes de finalizar
- Logs detalhados de processamento

### 4. **Testes de Integração Completos**

#### 4.1 Estrutura de Teste
```
tests/Feature/ChatBot/
├── FlowIntegrationTest.php
├── ConversationTimeoutTest.php
├── WebhookProcessingTest.php
└── EndToEndFlowTest.php
```

#### 4.2 Cenários de Teste
- **Fluxo Completo**: Do webhook inicial até finalização
- **Timeout por Flow**: Diferentes configurações de timeout
- **Mensagens de Encerramento**: Envio correto das mensagens
- **Navegação Condicional**: Testes de botões e condições
- **Input Collection**: Coleta e validação de dados

## 📝 Plano de Implementação Detalhado

### **Fase 1: Extensão do Domain Flow (2-3 dias)**

#### Tarefas:
1. **Atualizar Domain Flow**
   - Adicionar campos `inactivity_minutes` e `ending_conversation_message`
   - Atualizar construtor e métodos de serialização

2. **Migration e Model**
   - Criar migration para novos campos
   - Atualizar Eloquent model

3. **Factory e Repository**
   - Atualizar `FlowFactory` com novos campos
   - Incluir campos em métodos de persistência

4. **Testes Unitários**
   - Testar criação de Flow com novos campos
   - Validar serialização/deserialização

### **Fase 2: Melhorar Timeout Service (3-4 dias)**

#### Tarefas:
1. **Timeout Específico por Flow**
   - Implementar `findTimedOutConversationsByFlow()`
   - Considerar `inactivity_minutes` de cada flow
   - Calcular timeout baseado na última interação

2. **Envio de Mensagem de Encerramento**
   - Implementar `handleTimeoutWithMessage()`
   - Integrar com WhatsApp API para envio
   - Substituição de variáveis na mensagem

3. **Atualizar Repository**
   - Métodos para buscar conversas por timeout específico
   - Queries otimizadas com joins

4. **Testes do Service**
   - Testar timeout por flow
   - Validar envio de mensagens
   - Cenários edge cases

### **Fase 3: Cron Job e Command (1-2 dias)**

#### Tarefas:
1. **Atualizar Console Kernel**
   - Adicionar agendamento a cada 5 minutos
   - Configurar sem sobreposição

2. **Melhorar Command**
   - Processar timeouts específicos por flow
   - Logs detalhados de processamento
   - Opções de dry-run e force

3. **Monitoramento**
   - Logs estruturados para debugging
   - Métricas de conversas processadas

### **Fase 4: Testes de Integração (4-5 dias)**

#### Tarefas:
1. **Setup de Teste**
   - Factories para todos os domains
   - Mocks para WhatsApp API
   - Database de teste isolada

2. **Teste de Fluxo Completo**
   - Simular webhook completo
   - Validar processamento end-to-end
   - Verificar navegação condicional

3. **Teste de Timeout**
   - Diferentes configurações por flow
   - Validar envio de mensagens de encerramento
   - Verificar cleanup correto

4. **Teste de Performance**
   - Processar múltiplas conversas
   - Validar tempo de resposta
   - Stress test do sistema

## 🧪 Plano de Testes Detalhado

### **Teste de Integração: Fluxo Completo**

#### Cenário: "Atendimento de Suporte Técnico"
```php
// tests/Feature/ChatBot/EndToEndFlowTest.php
public function test_complete_support_flow_with_timeout()
{
    // 1. Setup: Criar flow com 30 minutos de timeout
    $flow = FlowFactory::create([
        'inactivity_minutes' => 30,
        'ending_conversation_message' => 'Sua sessão expirou. Digite "oi" para começar novamente.'
    ]);
    
    // 2. Simular webhook inicial
    $webhookData = $this->createWebhookData('Preciso de ajuda');
    
    // 3. Processar e validar resposta
    $response = $this->chatBotService->processWebhook($webhookData);
    
    // 4. Simular interações do usuário
    $this->simulateUserInteraction('1', 'button_selection');
    $this->simulateUserInteraction('João Silva', 'text_input');
    
    // 5. Simular timeout (avançar tempo)
    Carbon::setTestNow(now()->addMinutes(31));
    
    // 6. Executar cleanup
    Artisan::call('chatbot:cleanup-conversations');
    
    // 7. Validar mensagem de encerramento enviada
    $this->assertMessageSent('Sua sessão expirou...');
    
    // 8. Validar conversa marcada como finished
    $conversation = $this->getConversation();
    $this->assertTrue($conversation->is_finished);
}
```

#### Estrutura do Teste:
1. **Setup de Dados**: Flow, PhoneNumber, Client
2. **Simulação de Webhook**: Dados reais do WhatsApp
3. **Interações Sequenciais**: Botões, inputs, navegação
4. **Timeout Simulation**: Manipulação de tempo
5. **Cleanup Execution**: Comando de limpeza
6. **Validações**: Mensagens, estados, logs

### **Casos de Teste Específicos**

#### 1. **Timeout Diferenciado por Flow**
- Flow A: 15 minutos
- Flow B: 60 minutos
- Flow C: 120 minutos
- Validar que cada um respeita seu timeout

#### 2. **Mensagens de Encerramento Personalizadas**
- Diferentes mensagens por flow
- Substituição de variáveis ({{client.name}})
- Validar envio correto

#### 3. **Navegação Condicional Complexa**
- Múltiplos níveis de condições
- Loops e retornos
- Validar fluxo correto

#### 4. **Recovery de Erros**
- Falha no envio de mensagem
- Timeout durante processamento
- Webhook malformado

## 📊 Métricas e Monitoramento

### **KPIs do Sistema**
1. **Taxa de Conclusão de Fluxos**: % de conversas que chegam ao fim
2. **Tempo Médio de Conversa**: Duração média das sessões
3. **Taxa de Timeout**: % de conversas que expiram
4. **Taxa de Erro**: % de falhas no processamento

### **Logs Estruturados**
```php
// Exemplo de log estruturado
Log::info('Conversation timeout processed', [
    'conversation_id' => $conversation->id,
    'flow_id' => $conversation->flow_id,
    'timeout_minutes' => $flow->inactivity_minutes,
    'last_interaction' => $lastInteraction->created_at,
    'ending_message_sent' => true,
    'processing_time_ms' => $processingTime
]);
```

## 🚀 Cronograma de Execução

| Fase | Duração | Entregáveis |
|------|---------|-------------|
| **Fase 1** | 2-3 dias | Domain Flow estendido, migrations, testes unitários |
| **Fase 2** | 3-4 dias | Timeout service melhorado, envio de mensagens |
| **Fase 3** | 1-2 dias | Cron job configurado, command melhorado |
| **Fase 4** | 4-5 dias | Testes de integração completos |
| **Total** | **10-14 dias** | Sistema 100% funcional com testes |

## ✅ Critérios de Aceitação

### **Sistema será considerado 100% funcional quando:**

1. ✅ **Timeout Configurável**: Cada flow pode ter seu próprio `inactivity_minutes`
2. ✅ **Mensagem de Encerramento**: Cada flow pode ter sua `ending_conversation_message`
3. ✅ **Cron Automático**: Cleanup executa automaticamente a cada 5 minutos
4. ✅ **Envio de Mensagem**: Sistema envia mensagem antes de encerrar conversa
5. ✅ **Testes Completos**: Cobertura de 90%+ em testes de integração
6. ✅ **Performance**: Processa 1000+ conversas em < 30 segundos
7. ✅ **Monitoramento**: Logs estruturados e métricas disponíveis
8. ✅ **Documentação**: Guias de uso e troubleshooting completos

## 🔧 Especificações Técnicas Detalhadas

### **1. Extensão do Domain Flow**

#### Migration:
```sql
-- database/migrations/add_timeout_fields_to_flows_table.php
ALTER TABLE flows
ADD COLUMN inactivity_minutes INT DEFAULT 60 COMMENT 'Timeout em minutos para conversas inativas',
ADD COLUMN ending_conversation_message TEXT COMMENT 'Mensagem enviada ao encerrar por timeout';
```

#### Domain Flow Atualizado:
```php
// app/Domains/ChatBot/Flow.php
public ?int $inactivity_minutes;
public ?string $ending_conversation_message;

public function __construct(
    // ... existing parameters
    ?int $inactivity_minutes = 60,
    ?string $ending_conversation_message = null,
    // ... rest of parameters
) {
    // ... existing assignments
    $this->inactivity_minutes = $inactivity_minutes;
    $this->ending_conversation_message = $ending_conversation_message;
}

public function getTimeoutMinutes(): int
{
    return $this->inactivity_minutes ?? 60; // Default 1 hour
}

public function getEndingMessage(): string
{
    return $this->ending_conversation_message ?? 'Sua conversa foi encerrada por inatividade.';
}
```

### **2. ConversationTimeoutService Melhorado**

```php
// app/Services/Meta/WhatsApp/ChatBot/Services/ConversationTimeoutService.php

public function findTimedOutConversationsByFlow(): array
{
    $conversations = $this->conversationRepository->findActiveConversationsWithFlow();
    $timedOut = [];

    foreach ($conversations as $conversation) {
        if ($this->isConversationTimedOut($conversation)) {
            $timedOut[] = $conversation;
        }
    }

    return $timedOut;
}

private function isConversationTimedOut(WhatsAppConversation $conversation): bool
{
    if (!$conversation->flow || $conversation->is_finished) {
        return false;
    }

    $timeoutMinutes = $conversation->flow->getTimeoutMinutes();
    $lastActivity = $this->getLastActivityTime($conversation);

    return $lastActivity->addMinutes($timeoutMinutes)->isPast();
}

public function processTimeoutsWithMessages(): array
{
    $timedOutConversations = $this->findTimedOutConversationsByFlow();
    $processed = [];

    foreach ($timedOutConversations as $conversation) {
        try {
            $result = $this->handleTimeoutWithMessage($conversation);
            $processed[] = $result;
        } catch (\Exception $e) {
            Log::error('Failed to process timeout for conversation', [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    return $processed;
}

private function handleTimeoutWithMessage(WhatsAppConversation $conversation): array
{
    // 1. Enviar mensagem de encerramento
    $endingMessage = $this->prepareEndingMessage($conversation);
    $messageSent = $this->sendEndingMessage($conversation, $endingMessage);

    // 2. Marcar conversa como finalizada
    $conversation->is_finished = true;
    $conversation->updated_at = now();

    // 3. Adicionar informações de timeout
    $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
    $conversationData['timeout_info'] = [
        'timed_out_at' => now()->toISOString(),
        'reason' => 'flow_timeout',
        'timeout_minutes' => $conversation->flow->getTimeoutMinutes(),
        'ending_message_sent' => $messageSent,
        'ending_message' => $endingMessage,
    ];
    $conversation->json = json_encode($conversationData);

    // 4. Salvar conversa
    $this->conversationRepository->save($conversation);

    return [
        'conversation_id' => $conversation->id,
        'flow_id' => $conversation->flow_id,
        'timeout_minutes' => $conversation->flow->getTimeoutMinutes(),
        'message_sent' => $messageSent,
        'processed_at' => now()->toISOString()
    ];
}
```

### **3. Command Melhorado**

```php
// app/Console/Commands/ChatBot/CleanupInactiveConversations.php

public function handle(): int
{
    $this->info('Starting ChatBot conversation timeout processing...');

    try {
        // Processar timeouts específicos por flow
        $result = $this->conversationTimeoutService->processTimeoutsWithMessages();

        $this->displayDetailedResults($result);

        return Command::SUCCESS;
    } catch (\Exception $e) {
        $this->error('Timeout processing failed: ' . $e->getMessage());
        return Command::FAILURE;
    }
}

private function displayDetailedResults(array $results): void
{
    $this->info("Timeout processing completed:");
    $this->line("- Conversations processed: " . count($results));

    $groupedByFlow = collect($results)->groupBy('flow_id');

    foreach ($groupedByFlow as $flowId => $flowResults) {
        $this->line("  Flow {$flowId}: " . count($flowResults) . " conversations");
    }

    $messagesSent = collect($results)->where('message_sent', true)->count();
    $this->line("- Ending messages sent: {$messagesSent}");
}
```

### **4. Agendamento no Kernel**

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule): void
{
    // ... existing schedules

    // ChatBot conversation timeout processing
    $schedule->command('chatbot:cleanup-conversations')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/chatbot-cleanup.log'));
}
```

---

**Próximos Passos**: Aguardar aprovação para iniciar implementação da Fase 1.
