{"name": "💬 WhatsApp Webhooks", "description": "**WhatsApp Business API Webhook Endpoints**\n\nComprehensive collection of webhook endpoints for Meta's WhatsApp Business API integration.\n\n**🔐 Security Features:**\n- HMAC SHA-256 signature validation\n- Organization-specific token verification\n- Global token fallback support\n- Comprehensive security logging\n\n**📨 Message Processing:**\n- Incoming message handling\n- Status update processing\n- Interactive message support\n- Multiple message batching\n- Outgoing message filtering\n\n**🏢 Organization Management:**\n- Multi-tenant organization support\n- Phone number identification\n- Automatic organization detection\n- Active/suspended organization filtering\n\n**🧪 Testing & Validation:**\n- Security validation tests\n- Payload structure validation\n- Error handling verification\n- Performance testing scenarios\n\n**📊 Logging & Monitoring:**\n- Comprehensive webhook logging\n- Security event tracking\n- Processing status monitoring\n- Error message capture\n\n**Required Configuration:**\n- `WHATSAPP_WEBHOOK_SECRET`: For signature validation\n- `WHATSAPP_WEBHOOK_VERIFY_TOKEN`: Global verification token\n- Organization-specific tokens in database\n\n**Variables to Set:**\n- `{{URL}}`: Your API base URL\n- `{{phone_number_id}}`: WhatsApp Business phone number ID\n- `{{organization_webhook_token}}`: Organization-specific verify token\n- `{{global_webhook_token}}`: Global verify token\n- `{{webhook_signature}}`: HMAC SHA-256 signature (sha256=...)"}