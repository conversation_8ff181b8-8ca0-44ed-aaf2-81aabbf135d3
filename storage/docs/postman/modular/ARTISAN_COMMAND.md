# 🚀 Postman Collection Builder - Artisan Command

## Overview

The `postman:build` Artisan command automatically generates the complete Postman collection from all modular JSON files, providing a seamless workflow for maintaining and building your API documentation.

## Command Signature

```bash
php artisan postman:build [options]
```

## Options

| Option | Description | Default |
|--------|-------------|---------|
| `--output=` | Output file path | `storage/docs/postman/ObvioAPI.postman_collection.json` |
| `--source=` | Source directory path | `storage/docs/postman/modular` |
| `--dry-run` | Show what would be built without creating the file | `false` |

## Usage Examples

### Basic Usage
```bash
# Build the collection with default settings
php artisan postman:build
```

### Dry Run (Preview)
```bash
# Preview what would be built without creating files
php artisan postman:build --dry-run
```

### Custom Output Path
```bash
# Build to a custom location
php artisan postman:build --output=/path/to/custom/collection.json
```

### Custom Source Directory
```bash
# Build from a different modular directory
php artisan postman:build --source=/path/to/custom/modular
```

## How It Works

### 1. **Metadata Loading**
- Loads collection metadata from `modular/meta.json`
- Sets up the base collection structure with info, schema, and identifiers

### 2. **Folder Processing**
- Processes folders in the correct order:
  1. 🔐 Authentication
  2. 👥 User Management  
  3. 🔗 Webhooks
  4. 📥 Import
  5. 💳 Subscription
  6. 📄 NFSe
  7. 📦 Inventory
  8. 🤖 ChatBot

### 3. **Item Loading**
- Loads folder definitions from `folders/*.json`
- Recursively loads request items from `items/*/`
- Handles both direct requests and subfolder structures
- Automatically maps subfolder names to display names with emojis

### 4. **Collection Assembly**
- Assembles the complete Postman collection structure
- Maintains proper hierarchy and relationships
- Preserves all request details, headers, and body content

### 5. **Output Generation**
- Generates properly formatted JSON with pretty printing
- Creates output directory if it doesn't exist
- Provides file size and success confirmation

## Output Structure

The generated collection follows this structure:

```json
{
  "info": {
    "_postman_id": "...",
    "name": "Obvio API - Complete Collection",
    "description": "...",
    "schema": "..."
  },
  "item": [
    {
      "name": "🔐 Authentication",
      "description": "...",
      "item": [...]
    },
    {
      "name": "👥 User Management", 
      "description": "...",
      "item": [
        {
          "name": "👤 Users",
          "item": [...]
        },
        {
          "name": "🏢 Organizations",
          "item": [...]
        }
      ]
    }
  ]
}
```

## Features

### ✅ **Smart Folder Mapping**
- Automatically maps folder names to display names with emojis
- Handles nested subfolder structures
- Preserves folder descriptions and metadata

### ✅ **Error Handling**
- Validates JSON syntax in all files
- Provides clear error messages for debugging
- Gracefully handles missing files with warnings

### ✅ **Flexible Configuration**
- Customizable source and output paths
- Dry-run mode for testing
- Preserves original collection metadata

### ✅ **Progress Reporting**
- Shows detailed progress during build process
- Reports item counts per folder
- Displays final collection statistics

## Build Statistics

When you run the command, you'll see output like:

```
🚀 Building Postman collection from modular files...

📋 Loaded collection metadata: Obvio API - Complete Collection
  ✓ 🔐 Authentication: 9 items
  ✓ 👥 User Management: 19 items
  ✓ 🔗 Webhooks: 7 items
  ✓ 📥 Import: 6 items
  ✓ 💳 Subscription: 1 items
  ✓ 📄 NFSe: 4 items
  ✓ 📦 Inventory: 50 items
  ✓ 🤖 ChatBot: 58 items
📁 Built collection with 8 main folders
💾 Collection saved: 276.13 KB
✅ Collection built successfully
```

## Integration with Development Workflow

### **Git Hooks**
Add to your pre-commit hook:
```bash
#!/bin/sh
php artisan postman:build
git add storage/docs/postman/ObvioAPI.postman_collection.json
```

### **CI/CD Pipeline**
```yaml
- name: Build Postman Collection
  run: php artisan postman:build
  
- name: Validate Collection
  run: php artisan postman:build --dry-run
```

### **Development Script**
```bash
#!/bin/bash
# Update collection after modular changes
php artisan postman:build
echo "✅ Postman collection updated successfully!"
```

## Troubleshooting

### Common Issues

1. **Invalid JSON Error**
   - Check syntax in modular JSON files
   - Use `--dry-run` to identify problematic files

2. **Missing Files Warning**
   - Ensure all folder definition files exist
   - Check file naming conventions

3. **Permission Errors**
   - Verify write permissions on output directory
   - Check file ownership and permissions

### Debug Mode
```bash
# Run with verbose output
php artisan postman:build --dry-run -v
```

## File Structure Requirements

The command expects this modular structure:
```
storage/docs/postman/modular/
├── meta.json                    # Required: Collection metadata
├── folders/                     # Required: Folder definitions
│   ├── authentication.json
│   ├── user_management.json
│   └── ...
└── items/                       # Required: Request items
    ├── authentication/
    ├── user_management/
    │   ├── users/
    │   └── ...
    └── ...
```

This command provides a complete automation solution for maintaining your Postman collection from modular source files! 🎉
