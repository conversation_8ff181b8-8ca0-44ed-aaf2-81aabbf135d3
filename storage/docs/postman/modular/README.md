# Modular Postman Collection Structure

This directory contains a modular breakdown of the Obvio API Postman collection, where each folder and request is represented as individual JSON files for better Git tracking, collaboration, and reusability.

## 📁 Directory Structure

```
storage/docs/postman/modular/
├── meta.json                    # Collection metadata (name, description, schema)
├── folders/                     # Folder definitions
│   ├── authentication.json
│   ├── user_management.json
│   ├── webhooks.json
│   ├── import.json
│   ├── subscription.json
│   ├── nfse.json
│   ├── inventory.json
│   └── chatbot.json
└── items/                       # Individual request definitions
    ├── authentication/
    ├── user_management/
    │   ├── users/
    │   ├── organizations/
    │   └── profiles/
    ├── webhooks/
    │   ├── telegram/
    │   └── whatsapp/
    ├── import/
    ├── subscription/
    ├── nfse/
    ├── inventory/
    │   ├── brands/
    │   ├── products/
    │   ├── clients/
    │   ├── projects/
    │   ├── budgets/
    │   ├── stocks/
    │   ├── sales/
    │   ├── items/
    │   ├── shops/
    │   └── batches/
    └── chatbot/
        ├── flows/
        ├── steps/
        ├── components/
        ├── buttons/
        ├── campaigns/
        ├── messages/
        ├── templates/
        ├── parameters/
        ├── interactions/
        └── conversations/
```

## 🔐 Authentication
- **Location**: `items/authentication/`
- **Endpoints**: Login, Register, Logout, Password Reset, User Profile, Account Deletion
- **Features**: Automatic token extraction and environment variable setting

## 👥 User Management
- **Location**: `items/user_management/`
- **Subfolders**:
  - **Users**: CRUD operations, notifications management
  - **Organizations**: Read-only operations (create/update/delete unavailable)
  - **Profiles**: Read-only operations (create/update/delete unavailable)

## 🔗 Webhooks
- **Location**: `items/webhooks/`
- **Subfolders**:
  - **Telegram**: Bot message receiving endpoints
  - **WhatsApp**: Meta Business API webhook verification and message receiving

## 📥 Import
- **Location**: `items/import/`
- **Features**: File upload (Excel/CSV), bulk data import for various models
- **Supported Models**: products, brands, groups, clients, projects, budgets, stocks, etc.

## 💳 Subscription
- **Location**: `items/subscription/`
- **Status**: ⚠️ Endpoints not implemented yet

## 📄 NFSe
- **Location**: `items/nfse/`
- **Status**: ⚠️ Endpoints not implemented yet
- **Features**: Electronic Service Invoice management placeholders

## 📦 Inventory
- **Location**: `items/inventory/`
- **Comprehensive Features**:
  - **Brands**: Brand management with CRUD operations
  - **Products**: Product management with brand and stock filtering
  - **Clients**: Client management with CRUD operations
  - **Projects**: Project management with client and budget filtering
  - **Budgets**: Budget management with client and project filtering
  - **Stocks**: Stock management with product filtering and entry/exit tracking
  - **Sales**: Sales management with client and project filtering
  - **Items**: Item management with product, sale, and budget filtering
  - **Shops**: Shop management with CRUD operations
  - **Batches**: Batch management with stock entry and exit tracking

## 🤖 ChatBot
- **Location**: `items/chatbot/`
- **Comprehensive Features**:
  - **Flows**: Conversation flow management
  - **Steps**: Individual step management within flows
  - **Components**: Message components and templates
  - **Buttons**: Interactive button management
  - **Campaigns**: WhatsApp marketing campaigns
  - **Messages**: Individual message management
  - **Templates**: WhatsApp message template management
  - **Parameters**: Dynamic parameter management
  - **Interactions**: User interaction tracking
  - **Conversations**: Chat session management

## ✅ Benefits of This Structure

1. **Better Git Tracking**: Each endpoint change is tracked individually
2. **Easier Collaboration**: No merge conflicts on large JSON files
3. **Modular Testing**: Test specific folders or endpoints independently
4. **Custom Workflows**: Build automation tools (e.g., `php artisan postman:build`)
5. **Organized Development**: Clear separation of concerns by feature area

## 🚀 Usage

Each JSON file contains a complete Postman request definition that can be:
- Imported individually into Postman
- Combined programmatically to rebuild the full collection
- Used for automated testing and CI/CD pipelines
- Modified independently without affecting other endpoints

## 📋 File Naming Convention

- **Folders**: `{main_folder}_{subfolder}.json` (e.g., `user_management_users.json`)
- **Requests**: `{action}_{resource}.json` (e.g., `get_all_users.json`, `create_campaign.json`)
- **Special Actions**: `{special_action}_{resource}.json` (e.g., `save_full_template.json`)

## 🔧 Environment Variables

All requests use the following environment variables:
- `{{URL}}`: Base API URL
- `{{TOKEN}}`: Authentication bearer token (auto-set by login requests)
- Additional variables as needed per endpoint

This modular structure provides maximum flexibility for API development, testing, and documentation while maintaining the complete functionality of the original Postman collection.
