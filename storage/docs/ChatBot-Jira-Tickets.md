# ChatBot Refatoração - Tickets Jira

## 📋 Épico Principal

**EPIC-001: Refatoração Completa do Sistema ChatBot**
- **Objetivo**: Transformar o ChatBot de MVP básico em sistema enterprise-ready
- **Duração Estimada**: 16-22 dias
- **Prioridade**: Alta
- **Componentes**: ChatBot, WhatsApp Integration, Flow Management

---

## 🎯 FASE 1: Reestruturação de Domains (5-7 dias)

### **TICKET-001: Criar Enums e Value Objects para ChatBot**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Domains  

**Descrição:**
Criar enums tipados e value objects para substituir constantes string e melhorar type safety do sistema ChatBot.

**Tarefas Técnicas:**
- Criar `StepType` enum (MESSAGE, INTERACTIVE, INPUT, COMMAND, CONDITION, WEBHOOK, DELAY)
- <PERSON><PERSON><PERSON> `WhatsAppButtonType` enum (REPLY, URL, PHONE_NUMBER, COPY_CODE, FLOW)
- Criar `InteractiveType` enum (BUTTON, LIST, FLOW)
- Criar `FlowStatus` enum (DRAFT, ACTIVE, ARCHIVED)
- Criar `ComponentFormat` enum (TEXT, IMAGE, VIDEO, DOCUMENT)

**Acceptance Criteria:**
- [ ] Todos os enums criados com valores conformes à WhatsApp Business API
- [ ] Enums implementam métodos de validação e conversão
- [ ] Testes unitários para cada enum com 100% de cobertura
- [ ] Documentação PHPDoc completa para todos os enums
- [ ] Backward compatibility mantida durante transição

**Definição de Pronto:**
- Código revisado e aprovado
- Testes passando
- Documentação atualizada

---

### **TICKET-002: Refatorar Step Domain com Nova Arquitetura**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 3 dias  
**Componente**: ChatBot/Domains  

**Descrição:**
Refatorar completamente o Step Domain eliminando múltiplos booleanos confusos e implementando navegação condicional robusta.

**Tarefas Técnicas:**
- Remover campos booleanos (`is_message`, `is_interactive`, `is_command`, `is_input`)
- Adicionar campo `type` usando `StepType` enum
- Implementar `configuration` array tipado por tipo de step
- Adicionar `navigation_rules` para navegação condicional
- Adicionar `timeout_seconds` para timeout específico por step
- Criar métodos de validação de configuração por tipo

**Acceptance Criteria:**
- [ ] Step Domain usa apenas `StepType` enum ao invés de múltiplos booleanos
- [ ] Configuração tipada implementada para cada tipo de step
- [ ] Sistema de navegação condicional funcional com múltiplas regras
- [ ] Timeout por step implementado e testado
- [ ] Migration criada para converter dados existentes
- [ ] Factory atualizada para nova estrutura
- [ ] Repository atualizado com novos métodos de busca
- [ ] Testes unitários cobrindo todos os cenários de navegação

**Definição de Pronto:**
- Migration executada com sucesso
- Todos os testes passando
- Dados existentes migrados corretamente

---

### **TICKET-003: Criar StepNavigation Domain**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Domains  

**Descrição:**
Criar novo domain StepNavigation para gerenciar regras de navegação condicional entre steps de forma estruturada.

**Tarefas Técnicas:**
- Criar `StepNavigation` domain com relacionamentos
- Implementar tipos de condição (button_click, text_match, regex, default)
- Criar sistema de prioridades para ordenação de condições
- Implementar método `matchesCondition()` para validação
- Criar factory e repository para StepNavigation

**Acceptance Criteria:**
- [ ] StepNavigation domain criado com todos os campos necessários
- [ ] Tipos de condição implementados e testados
- [ ] Sistema de prioridades funcional
- [ ] Método `matchesCondition()` valida corretamente todas as condições
- [ ] Relacionamento com Step estabelecido
- [ ] Migration criada para nova tabela
- [ ] Factory e Repository implementados
- [ ] Testes unitários com cobertura completa

**Definição de Pronto:**
- Domain totalmente funcional
- Integração com Step testada
- Documentação completa

---

### **TICKET-004: Refatorar Button Domain para WhatsApp Compliance**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Domains  

**Descrição:**
Refatorar Button Domain para estar 100% conforme com WhatsApp Business API, incluindo validações de limites e tipos corretos.

**Tarefas Técnicas:**
- Substituir constantes string por `WhatsAppButtonType` enum
- Implementar validação de título (máximo 20 caracteres)
- Adicionar campos específicos por tipo (url, phone_number)
- Criar método `toWhatsAppPayload()` conforme API
- Implementar validação de máximo 3 botões reply
- Adicionar suporte a copy_code e flow buttons

**Acceptance Criteria:**
- [ ] Button Domain usa `WhatsAppButtonType` enum
- [ ] Validação de título com máximo 20 caracteres
- [ ] Campos específicos por tipo implementados
- [ ] Método `toWhatsAppPayload()` gera payload conforme WhatsApp API
- [ ] Validação de máximo 3 botões reply por mensagem
- [ ] Suporte a todos os tipos de botão do WhatsApp
- [ ] Testes unitários para cada tipo de botão
- [ ] Testes de validação de limites

**Definição de Pronto:**
- Payload gerado conforme documentação WhatsApp
- Todas as validações funcionando
- Testes passando

---

### **TICKET-005: Criar InteractiveMessage Domain**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 3 dias  
**Componente**: ChatBot/Domains  

**Descrição:**
Criar novo domain InteractiveMessage para gerenciar mensagens interativas (buttons e lists) conforme WhatsApp Business API.

**Tarefas Técnicas:**
- Criar `InteractiveMessage` domain com `InteractiveType` enum
- Implementar suporte a Button Messages (máximo 3 botões)
- Implementar suporte a List Messages (máximo 10 opções)
- Criar `ListSection` e `ListRow` domains
- Implementar validações de limites por tipo
- Criar métodos de conversão para WhatsApp API payload

**Acceptance Criteria:**
- [ ] InteractiveMessage domain criado com tipos BUTTON e LIST
- [ ] Button Messages suportam máximo 3 botões
- [ ] List Messages suportam máximo 10 opções em seções
- [ ] ListSection e ListRow domains implementados
- [ ] Validações de limites funcionais
- [ ] Métodos `toWhatsAppPayload()` conformes à API
- [ ] Factory e Repository implementados
- [ ] Migration criada
- [ ] Testes unitários cobrindo todos os cenários
- [ ] Testes de validação de limites

**Definição de Pronto:**
- Mensagens interativas funcionais
- Compliance 100% com WhatsApp API
- Validações robustas implementadas

---

### **TICKET-006: Estender Flow Domain com Campos de Timeout e Versionamento**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Domains  

**Descrição:**
Estender Flow Domain com campos necessários para timeout configurável, versionamento e gestão de estado.

**Tarefas Técnicas:**
- Adicionar `inactivity_minutes` para timeout configurável
- Adicionar `ending_conversation_message` para mensagem de encerramento
- Adicionar `version` para versionamento de flows
- Adicionar `status` usando `FlowStatus` enum
- Adicionar `variables` array para variáveis globais
- Implementar métodos de validação de integridade do flow

**Acceptance Criteria:**
- [ ] Campo `inactivity_minutes` implementado com default 60
- [ ] Campo `ending_conversation_message` para personalização
- [ ] Sistema de versionamento implementado
- [ ] Status do flow gerenciado via enum
- [ ] Variáveis globais suportadas
- [ ] Método `validateFlowIntegrity()` implementado
- [ ] Métodos `getTimeoutMinutes()` e `getEndingMessage()`
- [ ] Migration criada para novos campos
- [ ] Factory e Repository atualizados
- [ ] Testes unitários para todos os novos campos

**Definição de Pronto:**
- Todos os campos funcionais
- Validações implementadas
- Backward compatibility mantida

---

## 🎯 FASE 2: Refatoração de Use Cases (4-5 dias)

### **TICKET-007: Implementar Strategy Pattern para Step Processors**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 3 dias  
**Componente**: ChatBot/UseCases  

**Descrição:**
Refatorar ProcessFlowStep monolítico implementando Strategy Pattern com processors específicos para cada tipo de step.

**Tarefas Técnicas:**
- Criar `StepProcessorInterface` com método `process()`
- Implementar `MessageStepProcessor` para steps de mensagem
- Implementar `InteractiveStepProcessor` para steps interativos
- Implementar `InputStepProcessor` para coleta de dados
- Implementar `CommandStepProcessor` para comandos
- Implementar `ConditionStepProcessor` para lógica condicional
- Refatorar `ProcessFlowStep` para usar strategy pattern

**Acceptance Criteria:**
- [ ] `StepProcessorInterface` criada com contrato claro
- [ ] Processor específico para cada tipo de step
- [ ] `MessageStepProcessor` processa mensagens simples
- [ ] `InteractiveStepProcessor` gerencia botões e listas
- [ ] `InputStepProcessor` coleta e valida dados do usuário
- [ ] `CommandStepProcessor` executa comandos de negócio
- [ ] `ConditionStepProcessor` avalia condições e navega
- [ ] `ProcessFlowStep` refatorado usando strategy pattern
- [ ] Testes unitários para cada processor
- [ ] Testes de integração do sistema completo

**Definição de Pronto:**
- Código limpo e modular
- Fácil extensão para novos tipos
- Performance mantida ou melhorada

---

### **TICKET-008: Criar WhatsAppMessageBuilder para Compliance com API**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Services  

**Descrição:**
Criar builder especializado para construir payloads de mensagens WhatsApp conformes com a API oficial.

**Tarefas Técnicas:**
- Criar `WhatsAppMessageBuilder` com métodos específicos
- Implementar `buildTextMessage()` para mensagens simples
- Implementar `buildInteractiveButtonMessage()` para botões
- Implementar `buildInteractiveListMessage()` para listas
- Implementar `buildTemplateMessage()` para templates
- Adicionar validações de payload conforme API

**Acceptance Criteria:**
- [ ] `WhatsAppMessageBuilder` criado com interface clara
- [ ] `buildTextMessage()` gera payload correto
- [ ] `buildInteractiveButtonMessage()` conforme spec WhatsApp
- [ ] `buildInteractiveListMessage()` com validações de limite
- [ ] `buildTemplateMessage()` para templates aprovados
- [ ] Validações de payload implementadas
- [ ] Testes unitários para cada método
- [ ] Testes de compliance com WhatsApp API
- [ ] Documentação de uso completa

**Definição de Pronto:**
- Payloads 100% conformes com WhatsApp
- Validações robustas
- Fácil de usar e estender

---

## 🎯 FASE 3: Performance e Cache (2-3 dias)

### **TICKET-009: Implementar Sistema de Cache para Flows e Steps**
**Tipo**: Story  
**Prioridade**: Média  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Services  

**Descrição:**
Implementar sistema de cache inteligente para flows e steps visando eliminar N+1 queries e melhorar performance.

**Tarefas Técnicas:**
- Criar `FlowCacheService` com cache taggeado
- Implementar cache de flows com steps relacionados
- Implementar invalidação automática de cache
- Otimizar queries com eager loading
- Adicionar métricas de cache hit/miss

**Acceptance Criteria:**
- [ ] `FlowCacheService` implementado com cache Redis
- [ ] Cache de flows com TTL configurável
- [ ] Invalidação automática em updates
- [ ] Queries otimizadas com eager loading
- [ ] Métricas de performance implementadas
- [ ] Testes de performance com e sem cache
- [ ] Configuração de cache via environment
- [ ] Documentação de configuração

**Definição de Pronto:**
- Performance melhorada significativamente
- Cache funcionando corretamente
- Métricas disponíveis

---

### **TICKET-010: Implementar Queue para Processamento Assíncrono**
**Tipo**: Story  
**Prioridade**: Média  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Jobs  

**Descrição:**
Implementar sistema de queue para processamento assíncrono de webhooks, melhorando tempo de resposta e escalabilidade.

**Tarefas Técnicas:**
- Criar `ProcessWebhookMessageJob` para queue
- Implementar retry logic com backoff exponencial
- Configurar dead letter queue para falhas
- Adicionar monitoramento de queue
- Implementar fallback síncrono para casos críticos

**Acceptance Criteria:**
- [ ] `ProcessWebhookMessageJob` implementado
- [ ] Retry logic com 3 tentativas e backoff
- [ ] Dead letter queue configurada
- [ ] Monitoramento de queue implementado
- [ ] Fallback síncrono para casos críticos
- [ ] Testes de job com falhas simuladas
- [ ] Configuração de workers via environment
- [ ] Logs estruturados para debugging

**Definição de Pronto:**
- Queue funcionando em produção
- Monitoramento ativo
- Fallbacks testados

---

## 🎯 FASE 4: Timeout e Cleanup (2-3 dias)

### **TICKET-011: Refatorar ConversationTimeoutService**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Services  

**Descrição:**
Refatorar ConversationTimeoutService para suportar timeout configurável por flow e step, com envio de mensagens personalizadas.

**Tarefas Técnicas:**
- Implementar timeout específico por flow
- Implementar timeout específico por step
- Adicionar envio de mensagem de encerramento
- Implementar logs estruturados de timeout
- Otimizar queries de busca de conversas

**Acceptance Criteria:**
- [ ] Timeout por flow usando `inactivity_minutes`
- [ ] Timeout por step usando `timeout_seconds`
- [ ] Envio de `ending_conversation_message` personalizada
- [ ] Logs estruturados com contexto completo
- [ ] Queries otimizadas para busca de timeouts
- [ ] Testes unitários para diferentes cenários
- [ ] Testes de integração com envio de mensagens
- [ ] Métricas de timeout implementadas

**Definição de Pronto:**
- Timeout funcionando corretamente
- Mensagens sendo enviadas
- Logs estruturados disponíveis

---

### **TICKET-012: Implementar Cron Job Automático para Cleanup**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 1 dia  
**Componente**: ChatBot/Console  

**Descrição:**
Implementar e agendar cron job automático para cleanup de conversas inativas com logs detalhados e monitoramento.

**Tarefas Técnicas:**
- Agendar command no `Console/Kernel.php`
- Melhorar command com logs detalhados
- Implementar dry-run mode para testes
- Adicionar métricas de processamento
- Configurar alertas para falhas

**Acceptance Criteria:**
- [ ] Cron job agendado para executar a cada 5 minutos
- [ ] Command com logs detalhados por flow
- [ ] Dry-run mode implementado
- [ ] Métricas de conversas processadas
- [ ] Alertas configurados para falhas
- [ ] Testes do command em ambiente isolado
- [ ] Documentação de troubleshooting
- [ ] Configuração via environment variables

**Definição de Pronto:**
- Cron job executando automaticamente
- Logs estruturados disponíveis
- Alertas funcionando

---

## 🎯 FASE 5: Testes e Validação (3-4 dias)

### **TICKET-013: Implementar Testes de Compliance com WhatsApp API**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Tests  

**Descrição:**
Criar suite completa de testes para validar compliance 100% com WhatsApp Business API.

**Tarefas Técnicas:**
- Criar testes de payload para cada tipo de mensagem
- Implementar testes de validação de limites
- Criar testes de interactive messages
- Implementar testes de template messages
- Adicionar testes de error handling

**Acceptance Criteria:**
- [ ] Testes de payload para text, interactive e template messages
- [ ] Validação de limites (3 botões, 10 list items, 20 chars)
- [ ] Testes de interactive button e list messages
- [ ] Testes de template messages com parâmetros
- [ ] Testes de error handling e fallbacks
- [ ] Cobertura de testes > 90%
- [ ] Testes executando em CI/CD
- [ ] Documentação de casos de teste

**Definição de Pronto:**
- Compliance 100% validada
- Testes passando consistentemente
- Cobertura adequada

---

### **TICKET-014: Implementar Testes de Integração End-to-End**
**Tipo**: Story  
**Prioridade**: Alta  
**Estimativa**: 2 dias  
**Componente**: ChatBot/Tests  

**Descrição:**
Criar testes de integração end-to-end simulando fluxos completos de conversação com timeout e cleanup.

**Tarefas Técnicas:**
- Criar teste de fluxo completo com múltiplos steps
- Implementar teste de timeout por flow
- Criar teste de navegação condicional
- Implementar teste de coleta de dados
- Adicionar teste de performance

**Acceptance Criteria:**
- [ ] Teste de fluxo completo do webhook ao encerramento
- [ ] Teste de timeout com mensagem personalizada
- [ ] Teste de navegação condicional complexa
- [ ] Teste de coleta e validação de dados
- [ ] Teste de performance com múltiplas conversas
- [ ] Mocks adequados para WhatsApp API
- [ ] Helpers para criação de dados de teste
- [ ] Documentação de cenários testados

**Definição de Pronto:**
- Fluxos end-to-end funcionais
- Todos os cenários cobertos
- Performance validada

---

## 📊 Resumo dos Tickets

| Fase | Tickets | Estimativa | Prioridade |
|------|---------|------------|------------|
| **Fase 1** | TICKET-001 a TICKET-006 | 14 dias | Alta |
| **Fase 2** | TICKET-007 a TICKET-008 | 5 dias | Alta |
| **Fase 3** | TICKET-009 a TICKET-010 | 4 dias | Média |
| **Fase 4** | TICKET-011 a TICKET-012 | 3 dias | Alta |
| **Fase 5** | TICKET-013 a TICKET-014 | 4 dias | Alta |
| **Total** | **14 tickets** | **30 dias** | - |

## 🎯 Dependências Entre Tickets

- TICKET-002 depende de TICKET-001 (enums)
- TICKET-003 depende de TICKET-002 (Step refatorado)
- TICKET-005 depende de TICKET-004 (Button refatorado)
- TICKET-007 depende de TICKET-002, TICKET-003, TICKET-005
- TICKET-008 depende de TICKET-004, TICKET-005
- TICKET-011 depende de TICKET-006
- TICKET-013 depende de TICKET-008
- TICKET-014 depende de todos os anteriores

## 📋 Templates para Criação no Jira

### **Epic Principal**
```
Título: EPIC: Refatoração Completa do Sistema ChatBot
Tipo: Epic
Prioridade: High
Componente: ChatBot

Descrição:
Transformar o ChatBot de MVP básico em sistema enterprise-ready, escalável e conforme às melhores práticas da indústria.

Objetivos Principais:
• Compliance 100% com WhatsApp Business API
• Arquitetura escalável com Strategy Pattern
• Performance otimizada com cache e queue
• Timeout configurável por flow e step
• Navegação condicional robusta

Fases do Projeto:
1. Reestruturação de Domains (5-7 dias)
2. Refatoração de Use Cases (4-5 dias)
3. Performance e Cache (2-3 dias)
4. Timeout e Cleanup (2-3 dias)
5. Testes e Validação (3-4 dias)

Estimativa Total: 16-22 dias
```

### **Exemplo de Story**
```
Título: Criar Enums e Value Objects para ChatBot
Tipo: Story
Prioridade: High
Componente: ChatBot/Domains
Epic Link: [Epic criado acima]
Estimativa: 2 dias

Descrição:
Criar enums tipados e value objects para substituir constantes string e melhorar type safety do sistema ChatBot.

Tarefas Técnicas:
• Criar StepType enum (MESSAGE, INTERACTIVE, INPUT, COMMAND, CONDITION, WEBHOOK, DELAY)
• Criar WhatsAppButtonType enum (REPLY, URL, PHONE_NUMBER, COPY_CODE, FLOW)
• Criar InteractiveType enum (BUTTON, LIST, FLOW)
• Criar FlowStatus enum (DRAFT, ACTIVE, ARCHIVED)
• Criar ComponentFormat enum (TEXT, IMAGE, VIDEO, DOCUMENT)

Acceptance Criteria:
□ Todos os enums criados com valores conformes à WhatsApp Business API
□ Enums implementam métodos de validação e conversão
□ Testes unitários para cada enum com 100% de cobertura
□ Documentação PHPDoc completa para todos os enums
□ Backward compatibility mantida durante transição

Definição de Pronto:
□ Código revisado e aprovado
□ Testes passando
□ Documentação atualizada
```

## 🚀 Ordem de Implementação Recomendada

### **Sprint 1 (5 dias) - Fundação**
1. TICKET-001: Criar Enums e Value Objects
2. TICKET-002: Refatorar Step Domain
3. TICKET-003: Criar StepNavigation Domain

### **Sprint 2 (5 dias) - Domains Avançados**
4. TICKET-004: Refatorar Button Domain
5. TICKET-005: Criar InteractiveMessage Domain
6. TICKET-006: Estender Flow Domain

### **Sprint 3 (5 dias) - Use Cases**
7. TICKET-007: Implementar Strategy Pattern
8. TICKET-008: Criar WhatsAppMessageBuilder
9. TICKET-009: Sistema de Cache

### **Sprint 4 (5 dias) - Performance e Timeout**
10. TICKET-010: Queue Assíncrono
11. TICKET-011: Refatorar TimeoutService
12. TICKET-012: Cron Job Automático

### **Sprint 5 (4 dias) - Testes e Validação**
13. TICKET-013: Testes de Compliance
14. TICKET-014: Testes End-to-End

## 📊 Métricas de Sucesso

### **Funcionalidade**
- ✅ 100% compliance com WhatsApp Business API
- ✅ Navegação condicional complexa funcionando
- ✅ Timeout configurável por flow e step
- ✅ Interactive messages (buttons, lists) funcionais

### **Performance**
- ✅ Tempo de resposta < 200ms por webhook
- ✅ Suporte a 1000+ webhooks/minuto
- ✅ Cache hit rate > 80%
- ✅ Queue processing sem backlog

### **Qualidade**
- ✅ Cobertura de testes > 90%
- ✅ 0 bugs críticos em produção
- ✅ Logs estruturados implementados
- ✅ Documentação completa

### **Manutenibilidade**
- ✅ Código limpo com responsabilidades claras
- ✅ Fácil extensão para novos tipos de step
- ✅ Arquitetura escalável implementada
- ✅ Padrões de projeto aplicados corretamente

---

**Documentação de Apoio:**
- `storage/docs/ChatBot-Status-e-Plano-Completo.md` - Análise completa e plano
- `storage/docs/ChatBot-Analise-Problemas-Criticos.md` - Problemas detalhados
- `storage/docs/ChatBot-Plano-Testes-Integracao.md` - Estratégia de testes
